{"version": 3, "sources": ["../../../src/presets/lara/organizationchart/index.ts"], "sourcesContent": ["import type { OrganizationChartDesignTokens, OrganizationChartTokenSections } from '@primeuix/themes/types/organizationchart';\n\nexport const root: OrganizationChartTokenSections.Root = {\n    gutter: '0.75rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const node: OrganizationChartTokenSections.Node = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    selectedColor: '{highlight.color}',\n    hoverColor: '{content.hover.color}',\n    padding: '1rem 1.25rem',\n    toggleablePadding: '1rem 1.25rem 1.5rem 1.25rem',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const nodeToggleButton: OrganizationChartTokenSections.NodeToggleButton = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    borderColor: '{content.border.color}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    size: '1.75rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const connector: OrganizationChartTokenSections.Connector = {\n    color: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    height: '24px'\n};\n\nexport default {\n    root,\n    node,\n    nodeToggleButton,\n    connector\n} satisfies OrganizationChartDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAA4C;AAAA,EACrD,QAAQ;AAAA,EACR,oBAAoB;AACxB;AAEO,IAAM,OAA4C;AAAA,EACrD,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,cAAc;AAClB;AAEO,IAAM,mBAAoE;AAAA,EAC7E,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,YAAsD;AAAA,EAC/D,OAAO;AAAA,EACP,cAAc;AAAA,EACd,QAAQ;AACZ;AAEA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}