{"version": 3, "sources": ["../../../src/presets/lara/megamenu/index.ts"], "sourcesContent": ["import type { MegaMenuDesignTokens, MegaMenuTokenSections } from '@primeuix/themes/types/megamenu';\n\nexport const root: MegaMenuTokenSections.Root = {\n    borderColor: 'transparent',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    gap: '0.5rem',\n    verticalOrientation: {\n        padding: '{navigation.list.padding}',\n        gap: '{navigation.list.gap}'\n    },\n    horizontalOrientation: {\n        padding: '0.75rem 1rem',\n        gap: '0.5rem'\n    },\n    transitionDuration: '{transition.duration}'\n};\n\nexport const baseItem: MegaMenuTokenSections.BaseItem = {\n    borderRadius: '{content.border.radius}',\n    padding: '0.75rem 1rem'\n};\n\nexport const item: MegaMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const overlay: MegaMenuTokenSections.Overlay = {\n    padding: '0',\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    shadow: '{overlay.navigation.shadow}',\n    gap: '0.5rem'\n};\n\nexport const submenu: MegaMenuTokenSections.Submenu = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const submenuLabel: MegaMenuTokenSections.SubmenuLabel = {\n    padding: '{navigation.submenu.label.padding}',\n    fontWeight: '{navigation.submenu.label.font.weight}',\n    background: '{navigation.submenu.label.background}',\n    color: '{navigation.submenu.label.color}'\n};\n\nexport const submenuIcon: MegaMenuTokenSections.SubmenuIcon = {\n    size: '{navigation.submenu.icon.size}',\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}',\n    activeColor: '{navigation.submenu.icon.active.color}'\n};\n\nexport const separator: MegaMenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport const mobileButton: MegaMenuTokenSections.MobileButton = {\n    borderRadius: '50%',\n    size: '2rem',\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    hoverBackground: '{content.hover.background}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: MegaMenuTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.50}'\n        }\n    },\n    dark: {\n        root: {\n            background: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    root,\n    baseItem,\n    item,\n    overlay,\n    submenu,\n    submenuLabel,\n    submenuIcon,\n    separator,\n    mobileButton,\n    colorScheme\n} satisfies MegaMenuDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,KAAK;AAAA,EACL,qBAAqB;AAAA,IACjB,SAAS;AAAA,IACT,KAAK;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,IACnB,SAAS;AAAA,IACT,KAAK;AAAA,EACT;AAAA,EACA,oBAAoB;AACxB;AAEO,IAAM,WAA2C;AAAA,EACpD,cAAc;AAAA,EACd,SAAS;AACb;AAEO,IAAM,OAAmC;AAAA,EAC5C,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AAAA,EACL,MAAM;AAAA,IACF,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACJ;AAEO,IAAM,UAAyC;AAAA,EAClD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACT;AAEO,IAAM,UAAyC;AAAA,EAClD,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,eAAmD;AAAA,EAC5D,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AACX;AAEO,IAAM,cAAiD;AAAA,EAC1D,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AACjB;AAEO,IAAM,YAA6C;AAAA,EACtD,aAAa;AACjB;AAEO,IAAM,eAAmD;AAAA,EAC5D,cAAc;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,MAAM;AAAA,MACF,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}