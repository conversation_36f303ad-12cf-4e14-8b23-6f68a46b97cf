{"version": 3, "sources": ["../../../src/presets/lara/image/index.ts"], "sourcesContent": ["import type { ImageDesignTokens, ImageTokenSections } from '@primeuix/themes/types/image';\n\nexport const root: ImageTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const preview: ImageTokenSections.Preview = {\n    icon: {\n        size: '1.5rem'\n    },\n    mask: {\n        background: '{mask.background}',\n        color: '{mask.color}'\n    }\n};\n\nexport const toolbar: ImageTokenSections.Toolbar = {\n    position: {\n        left: 'auto',\n        right: '1rem',\n        top: '1rem',\n        bottom: 'auto'\n    },\n    blur: '8px',\n    background: 'rgba(255,255,255,0.1)',\n    borderColor: 'rgba(255,255,255,0.2)',\n    borderWidth: '1px',\n    borderRadius: '{content.border.radius}',\n    padding: '.5rem',\n    gap: '0.5rem'\n};\n\nexport const action: ImageTokenSections.Action = {\n    hoverBackground: 'rgba(255,255,255,0.1)',\n    color: '{surface.50}',\n    hoverColor: '{surface.0}',\n    size: '3rem',\n    iconSize: '1.5rem',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport default {\n    root,\n    preview,\n    toolbar,\n    action\n} satisfies ImageDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAgC;AAAA,EACzC,oBAAoB;AACxB;AAEO,IAAM,UAAsC;AAAA,EAC/C,MAAM;AAAA,IACF,MAAM;AAAA,EACV;AAAA,EACA,MAAM;AAAA,IACF,YAAY;AAAA,IACZ,OAAO;AAAA,EACX;AACJ;AAEO,IAAM,UAAsC;AAAA,EAC/C,UAAU;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ;AAAA,EACA,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,SAAoC;AAAA,EAC7C,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEA,IAAO,gBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}