{"version": 3, "sources": ["../../../src/presets/lara/splitter/index.ts"], "sourcesContent": ["import type { SplitterDesignTokens, SplitterTokenSections } from '@primeuix/themes/types/splitter';\n\nexport const root: SplitterTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const gutter: SplitterTokenSections.Gutter = {\n    background: '{content.border.color}'\n};\n\nexport const handle: SplitterTokenSections.Handle = {\n    size: '24px',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: SplitterTokenSections.ColorScheme = {\n    light: {\n        handle: {\n            background: '{surface.400}'\n        }\n    },\n    dark: {\n        handle: {\n            background: '{surface.600}'\n        }\n    }\n};\n\nexport default {\n    root,\n    gutter,\n    handle,\n    colorScheme\n} satisfies SplitterDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,oBAAoB;AACxB;AAEO,IAAM,SAAuC;AAAA,EAChD,YAAY;AAChB;AAEO,IAAM,SAAuC;AAAA,EAChD,MAAM;AAAA,EACN,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}