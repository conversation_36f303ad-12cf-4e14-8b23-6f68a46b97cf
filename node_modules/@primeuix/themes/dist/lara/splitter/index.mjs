var o={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},r={background:"{content.border.color}"},n={size:"24px",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},t={light:{handle:{background:"{surface.400}"}},dark:{handle:{background:"{surface.600}"}}},c={root:o,gutter:r,handle:n,colorScheme:t};export{t as colorScheme,c as default,r as gutter,n as handle,o as root};//# sourceMappingURL=index.mjs.map