{"version": 3, "sources": ["../../../src/presets/lara/rating/index.ts"], "sourcesContent": ["import type { RatingDesignTokens, RatingTokenSections } from '@primeuix/themes/types/rating';\n\nexport const root: RatingTokenSections.Root = {\n    gap: '0.25rem',\n    transitionDuration: '{transition.duration}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const icon: RatingTokenSections.Icon = {\n    size: '1.25rem',\n    color: '{text.muted.color}',\n    hoverColor: '{primary.color}',\n    activeColor: '{primary.color}'\n};\n\nexport default {\n    root,\n    icon\n} satisfies RatingDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAiC;AAAA,EAC1C,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,OAAiC;AAAA,EAC1C,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AACjB;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}