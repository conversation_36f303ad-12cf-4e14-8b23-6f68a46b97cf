var o={borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},r={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},e={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},l={focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},t={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},c={light:{toolbar:{background:"{surface.50}"}},dark:{toolbar:{background:"{surface.800}"}}},d={toolbar:o,toolbarItem:r,overlay:e,overlayOption:l,content:t,colorScheme:c};export{c as colorScheme,t as content,d as default,e as overlay,l as overlayOption,o as toolbar,r as toolbarItem};//# sourceMappingURL=index.mjs.map