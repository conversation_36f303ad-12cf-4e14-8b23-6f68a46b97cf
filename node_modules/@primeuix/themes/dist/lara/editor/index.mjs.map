{"version": 3, "sources": ["../../../src/presets/lara/editor/index.ts"], "sourcesContent": ["import type { EditorDesignTokens, EditorTokenSections } from '@primeuix/themes/types/editor';\n\nexport const toolbar: EditorTokenSections.Toolbar = {\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const toolbarItem: EditorTokenSections.ToolbarItem = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    activeColor: '{primary.color}'\n};\n\nexport const overlay: EditorTokenSections.Overlay = {\n    background: '{overlay.select.background}',\n    borderColor: '{overlay.select.border.color}',\n    borderRadius: '{overlay.select.border.radius}',\n    color: '{overlay.select.color}',\n    shadow: '{overlay.select.shadow}',\n    padding: '{list.padding}'\n};\n\nexport const overlayOption: EditorTokenSections.OverlayOption = {\n    focusBackground: '{list.option.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const content: EditorTokenSections.Content = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}'\n};\n\nexport const colorScheme: EditorTokenSections.ColorScheme = {\n    light: {\n        toolbar: {\n            background: '{surface.50}'\n        }\n    },\n    dark: {\n        toolbar: {\n            background: '{surface.800}'\n        }\n    }\n};\n\nexport default {\n    toolbar,\n    toolbarItem,\n    overlay,\n    overlayOption,\n    content,\n    colorScheme\n} satisfies EditorDesignTokens;\n"], "mappings": ";AAEO,IAAM,UAAuC;AAAA,EAChD,aAAa;AAAA,EACb,cAAc;AAClB;AAEO,IAAM,cAA+C;AAAA,EACxD,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AACjB;AAEO,IAAM,UAAuC;AAAA,EAChD,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AACb;AAEO,IAAM,gBAAmD;AAAA,EAC5D,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,UAAuC;AAAA,EAChD,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAClB;AAEO,IAAM,cAA+C;AAAA,EACxD,OAAO;AAAA,IACH,SAAS;AAAA,MACL,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,SAAS;AAAA,MACL,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}