var o={transitionDuration:"{transition.duration}"},r={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}"},e={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.75rem 0"},d={gap:"0.5rem",fontWeight:"700"},c={width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},n={color:"{form.field.icon.color}"},t={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.375rem 0.625rem",borderRadius:"{content.border.radius}"},a={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.375rem 0.625rem",borderRadius:"{content.border.radius}"},i={borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},l={margin:"0.75rem 0 0 0"},f={padding:"0.375rem",fontWeight:"700",color:"{content.color}"},u={hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",padding:"0.375rem",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},s={margin:"0.75rem 0 0 0"},g={padding:"0.5rem",borderRadius:"{content.border.radius}"},h={margin:"0.75rem 0 0 0"},m={padding:"0.5rem",borderRadius:"{content.border.radius}"},b={padding:"0.75rem 0 0 0",borderColor:"{content.border.color}"},p={padding:"0.75rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},v={light:{dropdown:{background:"{surface.50}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}},k={root:o,panel:r,header:e,title:d,dropdown:c,inputIcon:n,selectMonth:t,selectYear:a,group:i,dayView:l,weekDay:f,date:u,monthView:s,month:g,yearView:h,year:m,buttonbar:b,timePicker:p,colorScheme:v};export{b as buttonbar,v as colorScheme,u as date,l as dayView,k as default,c as dropdown,i as group,e as header,n as inputIcon,g as month,s as monthView,r as panel,o as root,t as selectMonth,a as selectYear,p as timePicker,d as title,f as weekDay,m as year,h as yearView};//# sourceMappingURL=index.mjs.map