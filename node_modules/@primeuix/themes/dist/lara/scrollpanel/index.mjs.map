{"version": 3, "sources": ["../../../src/presets/lara/scrollpanel/index.ts"], "sourcesContent": ["import type { ScrollPanelDesignTokens, ScrollPanelTokenSections } from '@primeuix/themes/types/scrollpanel';\n\nexport const root: ScrollPanelTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const bar: ScrollPanelTokenSections.Bar = {\n    size: '9px',\n    borderRadius: '{border.radius.sm}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: ScrollPanelTokenSections.ColorScheme = {\n    light: {\n        bar: {\n            background: '{surface.200}'\n        }\n    },\n    dark: {\n        bar: {\n            background: '{surface.700}'\n        }\n    }\n};\n\nexport default {\n    root,\n    bar,\n    colorScheme\n} satisfies ScrollPanelDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAsC;AAAA,EAC/C,oBAAoB;AACxB;AAEO,IAAM,MAAoC;AAAA,EAC7C,MAAM;AAAA,EACN,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,cAAoD;AAAA,EAC7D,OAAO;AAAA,IACH,KAAK;AAAA,MACD,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,KAAK;AAAA,MACD,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}