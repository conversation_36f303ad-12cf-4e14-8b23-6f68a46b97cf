var r={transitionDuration:"{transition.duration}"},o={size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},s={light:{bar:{background:"{surface.200}"}},dark:{bar:{background:"{surface.700}"}}},a={root:r,bar:o,colorScheme:s};export{o as bar,s as colorScheme,a as default,r as root};//# sourceMappingURL=index.mjs.map