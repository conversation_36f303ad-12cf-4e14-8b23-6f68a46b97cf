{"version": 3, "sources": ["../../../src/presets/lara/inplace/index.ts"], "sourcesContent": ["import type { InplaceDesignTokens, InplaceTokenSections } from '@primeuix/themes/types/inplace';\n\nexport const root: InplaceTokenSections.Root = {\n    padding: '{form.field.padding.y} {form.field.padding.x}',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    },\n    transitionDuration: '{transition.duration}'\n};\n\nexport const display: InplaceTokenSections.Display = {\n    hoverBackground: '{content.hover.background}',\n    hoverColor: '{content.hover.color}'\n};\n\nexport default {\n    root,\n    display\n} satisfies InplaceDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAkC;AAAA,EAC3C,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,oBAAoB;AACxB;AAEO,IAAM,UAAwC;AAAA,EACjD,iBAAiB;AAAA,EACjB,YAAY;AAChB;AAEA,IAAO,kBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}