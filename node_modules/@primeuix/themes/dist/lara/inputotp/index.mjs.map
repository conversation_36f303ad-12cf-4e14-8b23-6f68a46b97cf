{"version": 3, "sources": ["../../../src/presets/lara/inputotp/index.ts"], "sourcesContent": ["import type { InputOtpDesignTokens, InputOtpTokenSections } from '@primeuix/themes/types/inputotp';\n\nexport const root: InputOtpTokenSections.Root = {\n    gap: '0.5rem'\n};\n\nexport const input: InputOtpTokenSections.Input = {\n    width: '2.5rem',\n    sm: {\n        width: '2rem'\n    },\n    lg: {\n        width: '3rem'\n    }\n};\n\nexport default {\n    root,\n    input\n} satisfies InputOtpDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,KAAK;AACT;AAEO,IAAM,QAAqC;AAAA,EAC9C,OAAO;AAAA,EACP,IAAI;AAAA,IACA,OAAO;AAAA,EACX;AAAA,EACA,IAAI;AAAA,IACA,OAAO;AAAA,EACX;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}