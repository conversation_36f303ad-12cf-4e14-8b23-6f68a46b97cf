{"version": 3, "sources": ["../../../src/presets/lara/knob/index.ts"], "sourcesContent": ["import type { KnobDesignTokens, KnobTokenSections } from '@primeuix/themes/types/knob';\n\nexport const root: KnobTokenSections.Root = {\n    transitionDuration: '{transition.duration}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const value: KnobTokenSections.Value = {\n    background: '{primary.color}'\n};\n\nexport const range: KnobTokenSections.Range = {\n    background: '{content.border.color}'\n};\n\nexport const text: KnobTokenSections.Text = {\n    color: '{text.muted.color}'\n};\n\nexport default {\n    root,\n    value,\n    range,\n    text\n} satisfies KnobDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAA+B;AAAA,EACxC,oBAAoB;AAAA,EACpB,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,QAAiC;AAAA,EAC1C,YAAY;AAChB;AAEO,IAAM,QAAiC;AAAA,EAC1C,YAAY;AAChB;AAEO,IAAM,OAA+B;AAAA,EACxC,OAAO;AACX;AAEA,IAAO,eAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}