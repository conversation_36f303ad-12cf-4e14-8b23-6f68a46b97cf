{"version": 3, "sources": ["../../../src/presets/lara/datatable/index.ts"], "sourcesContent": ["import type { DataTableDesignTokens, DataTableTokenSections } from '@primeuix/themes/types/datatable';\n\nexport const root: DataTableTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const header: DataTableTokenSections.Header = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '1px 0 1px 0',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const headerCell: DataTableTokenSections.HeaderCell = {\n    selectedBackground: '{highlight.background}',\n    borderColor: '{datatable.border.color}',\n    hoverColor: '{content.hover.color}',\n    selectedColor: '{highlight.color}',\n    gap: '0.5rem',\n    padding: '0.75rem 1rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    },\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const columnTitle: DataTableTokenSections.ColumnTitle = {\n    fontWeight: '700'\n};\n\nexport const row: DataTableTokenSections.Row = {\n    background: '{content.background}',\n    hoverBackground: '{content.hover.background}',\n    selectedBackground: '{highlight.background}',\n    color: '{content.color}',\n    hoverColor: '{content.hover.color}',\n    selectedColor: '{highlight.color}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: 'inset {focus.ring.shadow}'\n    }\n};\n\nexport const bodyCell: DataTableTokenSections.BodyCell = {\n    borderColor: '{datatable.border.color}',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const footerCell: DataTableTokenSections.FooterCell = {\n    borderColor: '{datatable.border.color}',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const columnFooter: DataTableTokenSections.ColumnFooter = {\n    fontWeight: '700'\n};\n\nexport const footer: DataTableTokenSections.Footer = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0',\n    padding: '0.75rem 1rem',\n    sm: {\n        padding: '0.375rem 0.5rem'\n    },\n    lg: {\n        padding: '1rem 1.25rem'\n    }\n};\n\nexport const dropPoint: DataTableTokenSections.DropPoint = {\n    color: '{primary.color}'\n};\n\nexport const columnResizer: DataTableTokenSections.ColumnResizer = {\n    width: '0.5rem'\n};\n\nexport const resizeIndicator: DataTableTokenSections.ResizeIndicator = {\n    width: '1px',\n    color: '{primary.color}'\n};\n\nexport const sortIcon: DataTableTokenSections.SortIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}',\n    size: '0.875rem'\n};\n\nexport const loadingIcon: DataTableTokenSections.LoadingIcon = {\n    size: '2rem'\n};\n\nexport const rowToggleButton: DataTableTokenSections.RowToggleButton = {\n    hoverBackground: '{content.hover.background}',\n    selectedHoverBackground: '{content.background}',\n    color: '{text.muted.color}',\n    hoverColor: '{text.color}',\n    selectedHoverColor: '{primary.color}',\n    size: '1.75rem',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const filter: DataTableTokenSections.Filter = {\n    inlineGap: '0.5rem',\n    overlaySelect: {\n        background: '{overlay.select.background}',\n        borderColor: '{overlay.select.border.color}',\n        borderRadius: '{overlay.select.border.radius}',\n        color: '{overlay.select.color}',\n        shadow: '{overlay.select.shadow}'\n    },\n    overlayPopover: {\n        background: '{overlay.popover.background}',\n        borderColor: '{overlay.popover.border.color}',\n        borderRadius: '{overlay.popover.border.radius}',\n        color: '{overlay.popover.color}',\n        shadow: '{overlay.popover.shadow}',\n        padding: '{overlay.popover.padding}',\n        gap: '0.5rem'\n    },\n    rule: {\n        borderColor: '{content.border.color}'\n    },\n    constraintList: {\n        padding: '{list.padding}',\n        gap: '{list.gap}'\n    },\n    constraint: {\n        focusBackground: '{list.option.focus.background}',\n        selectedBackground: '{list.option.selected.background}',\n        selectedFocusBackground: '{list.option.selected.focus.background}',\n        color: '{list.option.color}',\n        focusColor: '{list.option.focus.color}',\n        selectedColor: '{list.option.selected.color}',\n        selectedFocusColor: '{list.option.selected.focus.color}',\n        separator: {\n            borderColor: '{content.border.color}'\n        },\n        padding: '{list.option.padding}',\n        borderRadius: '{list.option.border.radius}'\n    }\n};\n\nexport const paginatorTop: DataTableTokenSections.PaginatorTop = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const paginatorBottom: DataTableTokenSections.PaginatorBottom = {\n    borderColor: '{datatable.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const colorScheme: DataTableTokenSections.ColorScheme = {\n    light: {\n        root: {\n            borderColor: '{content.border.color}'\n        },\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        },\n        row: {\n            stripedBackground: '{surface.50}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.100}'\n        }\n    },\n    dark: {\n        root: {\n            borderColor: '{surface.800}'\n        },\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        headerCell: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            color: '{text.color}'\n        },\n        footer: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        footerCell: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        },\n        row: {\n            stripedBackground: '{surface.950}'\n        },\n        bodyCell: {\n            selectedBorderColor: '{primary.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    headerCell,\n    columnTitle,\n    row,\n    bodyCell,\n    footerCell,\n    columnFooter,\n    footer,\n    dropPoint,\n    columnResizer,\n    resizeIndicator,\n    sortIcon,\n    loadingIcon,\n    rowToggleButton,\n    filter,\n    paginatorTop,\n    paginatorBottom,\n    colorScheme\n} satisfies DataTableDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAoC;AAAA,EAC7C,oBAAoB;AACxB;AAEO,IAAM,SAAwC;AAAA,EACjD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,aAAgD;AAAA,EACzD,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,KAAK;AAAA,EACL,SAAS;AAAA,EACT,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,cAAkD;AAAA,EAC3D,YAAY;AAChB;AAEO,IAAM,MAAkC;AAAA,EAC3C,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,WAA4C;AAAA,EACrD,aAAa;AAAA,EACb,SAAS;AAAA,EACT,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,aAAgD;AAAA,EACzD,aAAa;AAAA,EACb,SAAS;AAAA,EACT,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,eAAoD;AAAA,EAC7D,YAAY;AAChB;AAEO,IAAM,SAAwC;AAAA,EACjD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AAAA,EACA,IAAI;AAAA,IACA,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,YAA8C;AAAA,EACvD,OAAO;AACX;AAEO,IAAM,gBAAsD;AAAA,EAC/D,OAAO;AACX;AAEO,IAAM,kBAA0D;AAAA,EACnE,OAAO;AAAA,EACP,OAAO;AACX;AAEO,IAAM,WAA4C;AAAA,EACrD,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AACV;AAEO,IAAM,cAAkD;AAAA,EAC3D,MAAM;AACV;AAEO,IAAM,kBAA0D;AAAA,EACnE,iBAAiB;AAAA,EACjB,yBAAyB;AAAA,EACzB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,MAAM;AAAA,EACN,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,SAAwC;AAAA,EACjD,WAAW;AAAA,EACX,eAAe;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,gBAAgB;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,KAAK;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACF,aAAa;AAAA,EACjB;AAAA,EACA,gBAAgB;AAAA,IACZ,SAAS;AAAA,IACT,KAAK;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACR,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,WAAW;AAAA,MACP,aAAa;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,EAClB;AACJ;AAEO,IAAM,eAAoD;AAAA,EAC7D,aAAa;AAAA,EACb,aAAa;AACjB;AAEO,IAAM,kBAA0D;AAAA,EACnE,aAAa;AAAA,EACb,aAAa;AACjB;AAEO,IAAM,cAAkD;AAAA,EAC3D,OAAO;AAAA,IACH,MAAM;AAAA,MACF,aAAa;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACR,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACD,mBAAmB;AAAA,IACvB;AAAA,IACA,UAAU;AAAA,MACN,qBAAqB;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,aAAa;AAAA,IACjB;AAAA,IACA,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACR,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,IACA,KAAK;AAAA,MACD,mBAAmB;AAAA,IACvB;AAAA,IACA,UAAU;AAAA,MACN,qBAAqB;AAAA,IACzB;AAAA,EACJ;AACJ;AAEA,IAAO,oBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}