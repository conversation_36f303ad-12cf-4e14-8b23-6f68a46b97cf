{"version": 3, "sources": ["../../../src/presets/lara/inputnumber/index.ts"], "sourcesContent": ["import type { InputNumberDesignTokens, InputNumberTokenSections } from '@primeuix/themes/types/inputnumber';\n\nexport const root: InputNumberTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const button: InputNumberTokenSections.Button = {\n    width: '2.5rem',\n    borderRadius: '{form.field.border.radius}',\n    verticalPadding: '{form.field.padding.y}'\n};\n\nexport const colorScheme: InputNumberTokenSections.ColorScheme = {\n    light: {\n        button: {\n            background: '{surface.100}',\n            hoverBackground: '{surface.200}',\n            activeBackground: '{surface.300}',\n            borderColor: '{form.field.border.color}',\n            hoverBorderColor: '{form.field.border.color}',\n            activeBorderColor: '{form.field.border.color}',\n            color: '{surface.600}',\n            hoverColor: '{surface.700}',\n            activeColor: '{surface.800}'\n        }\n    },\n    dark: {\n        button: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            activeBackground: '{surface.500}',\n            borderColor: '{form.field.border.color}',\n            hoverBorderColor: '{form.field.border.color}',\n            activeBorderColor: '{form.field.border.color}',\n            color: '{surface.300}',\n            hoverColor: '{surface.200}',\n            activeColor: '{surface.100}'\n        }\n    }\n};\n\nexport default {\n    root,\n    button,\n    colorScheme\n} satisfies InputNumberDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAsC;AAAA,EAC/C,oBAAoB;AACxB;AAEO,IAAM,SAA0C;AAAA,EACnD,OAAO;AAAA,EACP,cAAc;AAAA,EACd,iBAAiB;AACrB;AAEO,IAAM,cAAoD;AAAA,EAC7D,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,IACjB;AAAA,EACJ;AACJ;AAEA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}