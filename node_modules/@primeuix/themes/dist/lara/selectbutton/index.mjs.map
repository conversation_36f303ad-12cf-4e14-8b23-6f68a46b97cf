{"version": 3, "sources": ["../../../src/presets/lara/selectbutton/index.ts"], "sourcesContent": ["import type { SelectButtonDesignTokens, SelectButtonTokenSections } from '@primeuix/themes/types/selectbutton';\n\nexport const root: SelectButtonTokenSections.Root = {\n    borderRadius: '{form.field.border.radius}'\n};\n\nexport const colorScheme: SelectButtonTokenSections.ColorScheme = {\n    light: {\n        root: {\n            invalidBorderColor: '{form.field.invalid.border.color}'\n        }\n    },\n    dark: {\n        root: {\n            invalidBorderColor: '{form.field.invalid.border.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies SelectButtonDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAuC;AAAA,EAChD,cAAc;AAClB;AAEO,IAAM,cAAqD;AAAA,EAC9D,OAAO;AAAA,IACH,MAAM;AAAA,MACF,oBAAoB;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,oBAAoB;AAAA,IACxB;AAAA,EACJ;AACJ;AAEA,IAAO,uBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}