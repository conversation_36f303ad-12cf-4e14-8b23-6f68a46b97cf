{"version": 3, "sources": ["../../../src/presets/lara/dialog/index.ts"], "sourcesContent": ["import type { DialogDesignTokens, DialogTokenSections } from '@primeuix/themes/types/dialog';\n\nexport const root: DialogTokenSections.Root = {\n    background: '{overlay.modal.background}',\n    borderColor: '{overlay.modal.border.color}',\n    color: '{overlay.modal.color}',\n    borderRadius: '{overlay.modal.border.radius}',\n    shadow: '{overlay.modal.shadow}'\n};\n\nexport const header: DialogTokenSections.Header = {\n    padding: '{overlay.modal.padding}',\n    gap: '0.5rem'\n};\n\nexport const title: DialogTokenSections.Title = {\n    fontSize: '1.25rem',\n    fontWeight: '600'\n};\n\nexport const content: DialogTokenSections.Content = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}'\n};\n\nexport const footer: DialogTokenSections.Footer = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}',\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    header,\n    title,\n    content,\n    footer\n} satisfies DialogDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAiC;AAAA,EAC1C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,EACd,QAAQ;AACZ;AAEO,IAAM,SAAqC;AAAA,EAC9C,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,QAAmC;AAAA,EAC5C,UAAU;AAAA,EACV,YAAY;AAChB;AAEO,IAAM,UAAuC;AAAA,EAChD,SAAS;AACb;AAEO,IAAM,SAAqC;AAAA,EAC9C,SAAS;AAAA,EACT,KAAK;AACT;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}