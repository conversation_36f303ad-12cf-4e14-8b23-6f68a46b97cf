{"version": 3, "sources": ["../../../src/presets/lara/inputgroup/index.ts"], "sourcesContent": ["import type { InputGroupDesignTokens, InputGroupTokenSections } from '@primeuix/themes/types/inputgroup';\n\nexport const addon: InputGroupTokenSections.Addon = {\n    borderRadius: '{form.field.border.radius}',\n    padding: '0.625rem 0.5rem',\n    minWidth: '2.75rem'\n};\n\nexport const colorScheme: InputGroupTokenSections.ColorScheme = {\n    light: {\n        addon: {\n            background: '{surface.50}',\n            borderColor: '{form.field.border.color}',\n            color: '{text.muted.color}'\n        }\n    },\n    dark: {\n        addon: {\n            background: '{surface.800}',\n            borderColor: '{form.field.border.color}',\n            color: '{text.muted.color}'\n        }\n    }\n};\n\nexport default {\n    addon,\n    colorScheme\n} satisfies InputGroupDesignTokens;\n"], "mappings": ";AAEO,IAAM,QAAuC;AAAA,EAChD,cAAc;AAAA,EACd,SAAS;AAAA,EACT,UAAU;AACd;AAEO,IAAM,cAAmD;AAAA,EAC5D,OAAO;AAAA,IACH,OAAO;AAAA,MACH,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,OAAO;AAAA,MACH,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAO,qBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}