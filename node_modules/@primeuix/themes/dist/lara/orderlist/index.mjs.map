{"version": 3, "sources": ["../../../src/presets/lara/orderlist/index.ts"], "sourcesContent": ["import type { OrderListDesignTokens, OrderListTokenSections } from '@primeuix/themes/types/orderlist';\n\nexport const root: OrderListTokenSections.Root = {\n    gap: '1.125rem'\n};\n\nexport const controls: OrderListTokenSections.Controls = {\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    controls\n} satisfies OrderListDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAoC;AAAA,EAC7C,KAAK;AACT;AAEO,IAAM,WAA4C;AAAA,EACrD,KAAK;AACT;AAEA,IAAO,oBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}