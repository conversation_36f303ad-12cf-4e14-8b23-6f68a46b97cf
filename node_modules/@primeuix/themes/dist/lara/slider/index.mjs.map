{"version": 3, "sources": ["../../../src/presets/lara/slider/index.ts"], "sourcesContent": ["import type { SliderDesignTokens, SliderTokenSections } from '@primeuix/themes/types/slider';\n\nexport const root: SliderTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const track: SliderTokenSections.Track = {\n    background: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    size: '3px'\n};\n\nexport const range: SliderTokenSections.Range = {\n    background: '{primary.color}'\n};\n\nexport const handle: SliderTokenSections.Handle = {\n    width: '16px',\n    height: '16px',\n    borderRadius: '50%',\n    background: '{primary.color}',\n    hoverBackground: '{primary.color}',\n    content: {\n        borderRadius: '50%',\n        hoverBackground: '{primary.color}',\n        width: '12px',\n        height: '12px',\n        shadow: 'none'\n    },\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    }\n};\n\nexport const colorScheme: SliderTokenSections.ColorScheme = {\n    light: {\n        handle: {\n            content: {\n                background: '{surface.0}'\n            }\n        }\n    },\n    dark: {\n        handle: {\n            content: {\n                background: '{surface.950}'\n            }\n        }\n    }\n};\n\nexport default {\n    root,\n    track,\n    range,\n    handle,\n    colorScheme\n} satisfies SliderDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAiC;AAAA,EAC1C,oBAAoB;AACxB;AAEO,IAAM,QAAmC;AAAA,EAC5C,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,MAAM;AACV;AAEO,IAAM,QAAmC;AAAA,EAC5C,YAAY;AAChB;AAEO,IAAM,SAAqC;AAAA,EAC9C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,SAAS;AAAA,IACL,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,cAA+C;AAAA,EACxD,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,SAAS;AAAA,QACL,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,SAAS;AAAA,QACL,YAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}