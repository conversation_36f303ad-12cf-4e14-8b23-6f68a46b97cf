{"version": 3, "sources": ["../../../src/presets/lara/progressspinner/index.ts"], "sourcesContent": ["import type { ProgressSpinnerDesignTokens, ProgressSpinnerTokenSections } from '@primeuix/themes/types/progressspinner';\n\nexport const colorScheme: ProgressSpinnerTokenSections.ColorScheme = {\n    light: {\n        root: {\n            colorOne: '{pink.500}',\n            colorTwo: '{sky.500}',\n            colorThree: '{emerald.500}',\n            colorFour: '{amber.500}'\n        }\n    },\n    dark: {\n        root: {\n            colorOne: '{pink.400}',\n            colorTwo: '{sky.400}',\n            colorThree: '{emerald.400}',\n            colorFour: '{amber.400}'\n        }\n    }\n};\n\nexport default {\n    colorScheme\n} satisfies ProgressSpinnerDesignTokens;\n"], "mappings": ";AAEO,IAAM,cAAwD;AAAA,EACjE,OAAO;AAAA,IACH,MAAM;AAAA,MACF,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,IACf;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,IACf;AAAA,EACJ;AACJ;AAEA,IAAO,0BAAQ;AAAA,EACX;AACJ;", "names": []}