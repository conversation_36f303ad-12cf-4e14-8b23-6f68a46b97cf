{"version": 3, "sources": ["../../../src/presets/lara/fieldset/index.ts"], "sourcesContent": ["import type { FieldsetDesignTokens, FieldsetTokenSections } from '@primeuix/themes/types/fieldset';\n\nexport const root: FieldsetTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}',\n    padding: '0.75rem 1.125rem 1.125rem 1.125rem',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const legend: FieldsetTokenSections.Legend = {\n    borderRadius: '{content.border.radius}',\n    borderWidth: '1px',\n    borderColor: '{content.border.color}',\n    padding: '0.625rem 0.875rem',\n    gap: '0.5rem',\n    fontWeight: '700',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const toggleIcon: FieldsetTokenSections.ToggleIcon = {\n    color: '{text.muted.color}',\n    hoverColor: '{text.hover.muted.color}'\n};\n\nexport const content: FieldsetTokenSections.Content = {\n    padding: '0'\n};\n\nexport const colorScheme: FieldsetTokenSections.ColorScheme = {\n    light: {\n        legend: {\n            background: '{surface.50}',\n            hoverBackground: '{surface.100}',\n            color: '{text.color}',\n            hoverColor: '{text.hover.color}'\n        }\n    },\n    dark: {\n        legend: {\n            background: '{surface.800}',\n            hoverBackground: '{surface.700}',\n            color: '{text.color}',\n            hoverColor: '{text.hover.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    legend,\n    toggleIcon,\n    content,\n    colorScheme\n} satisfies FieldsetDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,oBAAoB;AACxB;AAEO,IAAM,SAAuC;AAAA,EAChD,cAAc;AAAA,EACd,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,aAA+C;AAAA,EACxD,OAAO;AAAA,EACP,YAAY;AAChB;AAEO,IAAM,UAAyC;AAAA,EAClD,SAAS;AACb;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}