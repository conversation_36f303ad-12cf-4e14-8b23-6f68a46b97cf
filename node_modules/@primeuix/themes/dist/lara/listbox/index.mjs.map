{"version": 3, "sources": ["../../../src/presets/lara/listbox/index.ts"], "sourcesContent": ["import type { ListboxDesignTokens, ListboxTokenSections } from '@primeuix/themes/types/listbox';\n\nexport const root: ListboxTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    borderColor: '{form.field.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    shadow: '{form.field.shadow}',\n    borderRadius: '{form.field.border.radius}',\n    transitionDuration: '{form.field.transition.duration}'\n};\n\nexport const list: ListboxTokenSections.List = {\n    padding: '{list.padding}',\n    gap: '{list.gap}',\n    header: {\n        padding: '{list.header.padding}'\n    }\n};\n\nexport const option: ListboxTokenSections.Option = {\n    focusBackground: '{list.option.focus.background}',\n    selectedBackground: '{list.option.selected.background}',\n    selectedFocusBackground: '{list.option.selected.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    selectedColor: '{list.option.selected.color}',\n    selectedFocusColor: '{list.option.selected.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const optionGroup: ListboxTokenSections.OptionGroup = {\n    background: '{list.option.group.background}',\n    color: '{list.option.group.color}',\n    fontWeight: '{list.option.group.font.weight}',\n    padding: '{list.option.group.padding}'\n};\n\nexport const checkmark: ListboxTokenSections.Checkmark = {\n    color: '{list.option.color}',\n    gutterStart: '-0.5rem',\n    gutterEnd: '0.5rem'\n};\n\nexport const emptyMessage: ListboxTokenSections.EmptyMessage = {\n    padding: '{list.option.padding}'\n};\n\nexport const colorScheme: ListboxTokenSections.ColorScheme = {\n    light: {\n        option: {\n            stripedBackground: '{surface.50}'\n        }\n    },\n    dark: {\n        option: {\n            stripedBackground: '{surface.900}'\n        }\n    }\n};\n\nexport default {\n    root,\n    list,\n    option,\n    optionGroup,\n    checkmark,\n    emptyMessage,\n    colorScheme\n} satisfies ListboxDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAkC;AAAA,EAC3C,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,oBAAoB;AACxB;AAEO,IAAM,OAAkC;AAAA,EAC3C,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,IACJ,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,SAAsC;AAAA,EAC/C,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,cAAgD;AAAA,EACzD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AACb;AAEO,IAAM,YAA4C;AAAA,EACrD,OAAO;AAAA,EACP,aAAa;AAAA,EACb,WAAW;AACf;AAEO,IAAM,eAAkD;AAAA,EAC3D,SAAS;AACb;AAEO,IAAM,cAAgD;AAAA,EACzD,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,mBAAmB;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,mBAAmB;AAAA,IACvB;AAAA,EACJ;AACJ;AAEA,IAAO,kBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}