{"version": 3, "sources": ["../../../src/presets/lara/contextmenu/index.ts"], "sourcesContent": ["import type { ContextMenuDesignTokens, ContextMenuTokenSections } from '@primeuix/themes/types/contextmenu';\n\nexport const root: ContextMenuTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const list: ContextMenuTokenSections.List = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const item: ContextMenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    activeBackground: '{navigation.item.active.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    activeColor: '{navigation.item.active.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}',\n        activeColor: '{navigation.item.icon.active.color}'\n    }\n};\n\nexport const submenu: ContextMenuTokenSections.Submenu = {\n    mobileIndent: '1.25rem'\n};\n\nexport const submenuIcon: ContextMenuTokenSections.SubmenuIcon = {\n    size: '{navigation.submenu.icon.size}',\n    color: '{navigation.submenu.icon.color}',\n    focusColor: '{navigation.submenu.icon.focus.color}',\n    activeColor: '{navigation.submenu.icon.active.color}'\n};\n\nexport const separator: ContextMenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport default {\n    root,\n    list,\n    item,\n    submenu,\n    submenuIcon,\n    separator\n} satisfies ContextMenuDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAsC;AAAA,EAC/C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,oBAAoB;AACxB;AAEO,IAAM,OAAsC;AAAA,EAC/C,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,OAAsC;AAAA,EAC/C,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AAAA,EACL,MAAM;AAAA,IACF,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACJ;AAEO,IAAM,UAA4C;AAAA,EACrD,cAAc;AAClB;AAEO,IAAM,cAAoD;AAAA,EAC7D,MAAM;AAAA,EACN,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AACjB;AAEO,IAAM,YAAgD;AAAA,EACzD,aAAa;AACjB;AAEA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}