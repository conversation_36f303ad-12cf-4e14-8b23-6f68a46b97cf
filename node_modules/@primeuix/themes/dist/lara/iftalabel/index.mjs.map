{"version": 3, "sources": ["../../../src/presets/lara/iftalabel/index.ts"], "sourcesContent": ["import type { IftaLabelDesignTokens, IftaLabelTokenSections } from '@primeuix/themes/types/iftalabel';\n\nexport const root: IftaLabelTokenSections.Root = {\n    color: '{form.field.float.label.color}',\n    focusColor: '{form.field.float.label.focus.color}',\n    invalidColor: '{form.field.float.label.invalid.color}',\n    transitionDuration: '0.2s',\n    positionX: '{form.field.padding.x}',\n    top: '{form.field.padding.y}',\n    fontSize: '0.75rem',\n    fontWeight: '400'\n};\n\nexport const input: IftaLabelTokenSections.Input = {\n    paddingTop: '1.875rem',\n    paddingBottom: '{form.field.padding.y}'\n};\n\nexport default {\n    root,\n    input\n} satisfies IftaLabelDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAoC;AAAA,EAC7C,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,KAAK;AAAA,EACL,UAAU;AAAA,EACV,YAAY;AAChB;AAEO,IAAM,QAAsC;AAAA,EAC/C,YAAY;AAAA,EACZ,eAAe;AACnB;AAEA,IAAO,oBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}