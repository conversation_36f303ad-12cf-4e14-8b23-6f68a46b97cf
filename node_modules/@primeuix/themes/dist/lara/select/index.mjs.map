{"version": 3, "sources": ["../../../src/presets/lara/select/index.ts"], "sourcesContent": ["import type { SelectDesignTokens, SelectTokenSections } from '@primeuix/themes/types/select';\n\nexport const root: SelectTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    filledHoverBackground: '{form.field.filled.hover.background}',\n    filledFocusBackground: '{form.field.filled.focus.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    placeholderColor: '{form.field.placeholder.color}',\n    invalidPlaceholderColor: '{form.field.invalid.placeholder.color}',\n    shadow: '{form.field.shadow}',\n    paddingX: '{form.field.padding.x}',\n    paddingY: '{form.field.padding.y}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        fontSize: '{form.field.sm.font.size}',\n        paddingX: '{form.field.sm.padding.x}',\n        paddingY: '{form.field.sm.padding.y}'\n    },\n    lg: {\n        fontSize: '{form.field.lg.font.size}',\n        paddingX: '{form.field.lg.padding.x}',\n        paddingY: '{form.field.lg.padding.y}'\n    }\n};\n\nexport const dropdown: SelectTokenSections.Dropdown = {\n    width: '2.5rem',\n    color: '{form.field.icon.color}'\n};\n\nexport const overlay: SelectTokenSections.Overlay = {\n    background: '{overlay.select.background}',\n    borderColor: '{overlay.select.border.color}',\n    borderRadius: '{overlay.select.border.radius}',\n    color: '{overlay.select.color}',\n    shadow: '{overlay.select.shadow}'\n};\n\nexport const list: SelectTokenSections.List = {\n    padding: '{list.padding}',\n    gap: '{list.gap}',\n    header: {\n        padding: '{list.header.padding}'\n    }\n};\n\nexport const option: SelectTokenSections.Option = {\n    focusBackground: '{list.option.focus.background}',\n    selectedBackground: '{list.option.selected.background}',\n    selectedFocusBackground: '{list.option.selected.focus.background}',\n    color: '{list.option.color}',\n    focusColor: '{list.option.focus.color}',\n    selectedColor: '{list.option.selected.color}',\n    selectedFocusColor: '{list.option.selected.focus.color}',\n    padding: '{list.option.padding}',\n    borderRadius: '{list.option.border.radius}'\n};\n\nexport const optionGroup: SelectTokenSections.OptionGroup = {\n    background: '{list.option.group.background}',\n    color: '{list.option.group.color}',\n    fontWeight: '{list.option.group.font.weight}',\n    padding: '{list.option.group.padding}'\n};\n\nexport const clearIcon: SelectTokenSections.ClearIcon = {\n    color: '{form.field.icon.color}'\n};\n\nexport const checkmark: SelectTokenSections.Checkmark = {\n    color: '{list.option.color}',\n    gutterStart: '-0.5rem',\n    gutterEnd: '0.5rem'\n};\n\nexport const emptyMessage: SelectTokenSections.EmptyMessage = {\n    padding: '{list.option.padding}'\n};\n\nexport default {\n    root,\n    dropdown,\n    overlay,\n    list,\n    option,\n    optionGroup,\n    clearIcon,\n    checkmark,\n    emptyMessage\n} satisfies SelectDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAiC;AAAA,EAC1C,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,oBAAoB;AAAA,EACpB,IAAI;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAAA,EACA,IAAI;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACJ;AAEO,IAAM,WAAyC;AAAA,EAClD,OAAO;AAAA,EACP,OAAO;AACX;AAEO,IAAM,UAAuC;AAAA,EAChD,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AACZ;AAEO,IAAM,OAAiC;AAAA,EAC1C,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,IACJ,SAAS;AAAA,EACb;AACJ;AAEO,IAAM,SAAqC;AAAA,EAC9C,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,cAA+C;AAAA,EACxD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AACb;AAEO,IAAM,YAA2C;AAAA,EACpD,OAAO;AACX;AAEO,IAAM,YAA2C;AAAA,EACpD,OAAO;AAAA,EACP,aAAa;AAAA,EACb,WAAW;AACf;AAEO,IAAM,eAAiD;AAAA,EAC1D,SAAS;AACb;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}