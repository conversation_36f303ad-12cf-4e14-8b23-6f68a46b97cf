{"version": 3, "sources": ["../../../src/presets/lara/drawer/index.ts"], "sourcesContent": ["import type { DrawerDesignTokens, DrawerTokenSections } from '@primeuix/themes/types/drawer';\n\nexport const root: DrawerTokenSections.Root = {\n    background: '{overlay.modal.background}',\n    borderColor: '{overlay.modal.border.color}',\n    color: '{overlay.modal.color}',\n    shadow: '{overlay.modal.shadow}'\n};\n\nexport const header: DrawerTokenSections.Header = {\n    padding: '{overlay.modal.padding}'\n};\n\nexport const title: DrawerTokenSections.Title = {\n    fontSize: '1.5rem',\n    fontWeight: '600'\n};\n\nexport const content: DrawerTokenSections.Content = {\n    padding: '0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}'\n};\n\nexport const footer: DrawerTokenSections.Footer = {\n    padding: '{overlay.modal.padding}'\n};\n\nexport default {\n    root,\n    header,\n    title,\n    content,\n    footer\n} satisfies DrawerDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAiC;AAAA,EAC1C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AACZ;AAEO,IAAM,SAAqC;AAAA,EAC9C,SAAS;AACb;AAEO,IAAM,QAAmC;AAAA,EAC5C,UAAU;AAAA,EACV,YAAY;AAChB;AAEO,IAAM,UAAuC;AAAA,EAChD,SAAS;AACb;AAEO,IAAM,SAAqC;AAAA,EAC9C,SAAS;AACb;AAEA,IAAO,iBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}