{"version": 3, "sources": ["../../../src/presets/lara/inputtext/index.ts"], "sourcesContent": ["import type { InputTextDesignTokens, InputTextTokenSections } from '@primeuix/themes/types/inputtext';\n\nexport const root: InputTextTokenSections.Root = {\n    background: '{form.field.background}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    filledHoverBackground: '{form.field.filled.hover.background}',\n    filledFocusBackground: '{form.field.filled.focus.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    color: '{form.field.color}',\n    disabledColor: '{form.field.disabled.color}',\n    placeholderColor: '{form.field.placeholder.color}',\n    invalidPlaceholderColor: '{form.field.invalid.placeholder.color}',\n    shadow: '{form.field.shadow}',\n    paddingX: '{form.field.padding.x}',\n    paddingY: '{form.field.padding.y}',\n    borderRadius: '{form.field.border.radius}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        fontSize: '{form.field.sm.font.size}',\n        paddingX: '{form.field.sm.padding.x}',\n        paddingY: '{form.field.sm.padding.y}'\n    },\n    lg: {\n        fontSize: '{form.field.lg.font.size}',\n        paddingX: '{form.field.lg.padding.x}',\n        paddingY: '{form.field.lg.padding.y}'\n    }\n};\n\nexport default {\n    root\n} satisfies InputTextDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAoC;AAAA,EAC7C,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,oBAAoB;AAAA,EACpB,IAAI;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AAAA,EACA,IAAI;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,EACd;AACJ;AAEA,IAAO,oBAAQ;AAAA,EACX;AACJ;", "names": []}