{"version": 3, "sources": ["../../../src/presets/lara/skeleton/index.ts"], "sourcesContent": ["import type { SkeletonDesignTokens, SkeletonTokenSections } from '@primeuix/themes/types/skeleton';\n\nexport const root: SkeletonTokenSections.Root = {\n    borderRadius: '{content.border.radius}'\n};\n\nexport const colorScheme: SkeletonTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: '{surface.200}',\n            animationBackground: 'rgba(255,255,255,0.4)'\n        }\n    },\n    dark: {\n        root: {\n            background: 'rgba(255, 255, 255, 0.06)',\n            animationBackground: 'rgba(255, 255, 255, 0.04)'\n        }\n    }\n};\n\nexport default {\n    root,\n    colorScheme\n} satisfies SkeletonDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,cAAc;AAClB;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,qBAAqB;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,qBAAqB;AAAA,IACzB;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}