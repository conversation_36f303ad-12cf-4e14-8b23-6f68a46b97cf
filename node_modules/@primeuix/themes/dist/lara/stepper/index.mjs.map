{"version": 3, "sources": ["../../../src/presets/lara/stepper/index.ts"], "sourcesContent": ["import type { StepperDesignTokens, StepperTokenSections } from '@primeuix/themes/types/stepper';\n\nexport const root: StepperTokenSections.Root = {\n    transitionDuration: '{transition.duration}'\n};\n\nexport const separator: StepperTokenSections.Separator = {\n    background: '{content.border.color}',\n    activeBackground: '{primary.color}',\n    margin: '0 0 0 1.625rem',\n    size: '2px'\n};\n\nexport const step: StepperTokenSections.Step = {\n    padding: '0.5rem',\n    gap: '1rem'\n};\n\nexport const stepHeader: StepperTokenSections.StepHeader = {\n    padding: '0',\n    borderRadius: '{content.border.radius}',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    },\n    gap: '0.5rem'\n};\n\nexport const stepTitle: StepperTokenSections.StepTitle = {\n    color: '{text.muted.color}',\n    activeColor: '{primary.color}',\n    fontWeight: '500'\n};\n\nexport const stepNumber: StepperTokenSections.StepNumber = {\n    background: '{content.background}',\n    activeBackground: '{primary.color}',\n    borderColor: '{content.border.color}',\n    activeBorderColor: '{primary.color}',\n    color: '{text.muted.color}',\n    activeColor: '{primary.contrast.color}',\n    size: '2.25rem',\n    fontSize: '1.125rem',\n    fontWeight: '500',\n    borderRadius: '50%',\n    shadow: 'none'\n};\n\nexport const steppanels: StepperTokenSections.Steppanels = {\n    padding: '0.875rem 0.5rem 1.125rem 0.5rem'\n};\n\nexport const steppanel: StepperTokenSections.Steppanel = {\n    background: '{content.background}',\n    color: '{content.color}',\n    padding: '0',\n    indent: '1rem'\n};\n\nexport default {\n    root,\n    separator,\n    step,\n    stepHeader,\n    stepTitle,\n    stepNumber,\n    steppanels,\n    steppanel\n} satisfies StepperDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAkC;AAAA,EAC3C,oBAAoB;AACxB;AAEO,IAAM,YAA4C;AAAA,EACrD,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,QAAQ;AAAA,EACR,MAAM;AACV;AAEO,IAAM,OAAkC;AAAA,EAC3C,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,aAA8C;AAAA,EACvD,SAAS;AAAA,EACT,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,KAAK;AACT;AAEO,IAAM,YAA4C;AAAA,EACrD,OAAO;AAAA,EACP,aAAa;AAAA,EACb,YAAY;AAChB;AAEO,IAAM,aAA8C;AAAA,EACvD,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,QAAQ;AACZ;AAEO,IAAM,aAA8C;AAAA,EACvD,SAAS;AACb;AAEO,IAAM,YAA4C;AAAA,EACrD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AACZ;AAEA,IAAO,kBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}