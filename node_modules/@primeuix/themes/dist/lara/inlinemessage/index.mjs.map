{"version": 3, "sources": ["../../../src/presets/lara/inlinemessage/index.ts"], "sourcesContent": ["import type { InlineMessageDesignTokens, InlineMessageTokenSections } from '@primeuix/themes/types/inlinemessage';\n\nexport const root: InlineMessageTokenSections.Root = {\n    padding: '{form.field.padding.y} {form.field.padding.x}',\n    borderRadius: '{content.border.radius}',\n    gap: '0.5rem'\n};\n\nexport const text: InlineMessageTokenSections.Text = {\n    fontWeight: '500'\n};\n\nexport const icon: InlineMessageTokenSections.Icon = {\n    size: '1.125rem'\n};\n\nexport const colorScheme: InlineMessageTokenSections.ColorScheme = {\n    light: {\n        info: {\n            background: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {blue.50}, transparent 5%)',\n            color: '{blue.600}',\n            shadow: 'none'\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {green.50}, transparent 5%)',\n            color: '{green.600}',\n            shadow: 'none'\n        },\n        warn: {\n            background: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb,{yellow.50}, transparent 5%)',\n            color: '{yellow.600}',\n            shadow: 'none'\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            borderColor: 'color-mix(in srgb, {red.50}, transparent 5%)',\n            color: '{red.600}',\n            shadow: 'none'\n        },\n        secondary: {\n            background: '{surface.100}',\n            borderColor: '{surface.100}',\n            color: '{surface.600}',\n            shadow: 'none'\n        },\n        contrast: {\n            background: '{surface.900}',\n            borderColor: '{surface.900}',\n            color: '{surface.50}',\n            shadow: 'none'\n        }\n    },\n    dark: {\n        info: {\n            background: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {blue.500}, transparent 84%)',\n            color: '{blue.500}',\n            shadow: 'none'\n        },\n        success: {\n            background: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {green.500}, transparent 84%)',\n            color: '{green.500}',\n            shadow: 'none'\n        },\n        warn: {\n            background: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {yellow.500}, transparent 84%)',\n            color: '{yellow.500}',\n            shadow: 'none'\n        },\n        error: {\n            background: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            borderColor: 'color-mix(in srgb, {red.500}, transparent 84%)',\n            color: '{red.500}',\n            shadow: 'none'\n        },\n        secondary: {\n            background: '{surface.800}',\n            borderColor: '{surface.800}',\n            color: '{surface.300}',\n            shadow: 'none'\n        },\n        contrast: {\n            background: '{surface.0}',\n            borderColor: '{surface.0}',\n            color: '{surface.950}',\n            shadow: 'none'\n        }\n    }\n};\n\nexport default {\n    root,\n    text,\n    icon,\n    colorScheme\n} satisfies InlineMessageDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAwC;AAAA,EACjD,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AACT;AAEO,IAAM,OAAwC;AAAA,EACjD,YAAY;AAChB;AAEO,IAAM,OAAwC;AAAA,EACjD,MAAM;AACV;AAEO,IAAM,cAAsD;AAAA,EAC/D,OAAO;AAAA,IACH,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACH,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,WAAW;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACF,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACH,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,WAAW;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AAEA,IAAO,wBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}