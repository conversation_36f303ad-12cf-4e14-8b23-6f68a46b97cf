{"version": 3, "sources": ["../../../src/presets/lara/progressbar/index.ts"], "sourcesContent": ["import type { ProgressBarDesignTokens, ProgressBarTokenSections } from '@primeuix/themes/types/progressbar';\n\nexport const root: ProgressBarTokenSections.Root = {\n    background: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    height: '1.5rem'\n};\n\nexport const value: ProgressBarTokenSections.Value = {\n    background: '{primary.color}'\n};\n\nexport const label: ProgressBarTokenSections.Label = {\n    color: '{primary.contrast.color}',\n    fontSize: '0.875rem',\n    fontWeight: '600'\n};\n\nexport default {\n    root,\n    value,\n    label\n} satisfies ProgressBarDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAsC;AAAA,EAC/C,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,QAAQ;AACZ;AAEO,IAAM,QAAwC;AAAA,EACjD,YAAY;AAChB;AAEO,IAAM,QAAwC;AAAA,EACjD,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAChB;AAEA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}