{"version": 3, "sources": ["../../../src/presets/lara/panel/index.ts"], "sourcesContent": ["import type { PanelDesignTokens, PanelTokenSections } from '@primeuix/themes/types/panel';\n\nexport const root: PanelTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    color: '{content.color}'\n};\n\nexport const header: PanelTokenSections.Header = {\n    borderWidth: '0 0 1px 0',\n    borderColor: '{content.border.color}',\n    padding: '1.125rem',\n    borderRadius: '5px 5px 0 0'\n};\n\nexport const toggleableHeader: PanelTokenSections.ToggleableHeader = {\n    padding: '0.25rem 1.125rem'\n};\n\nexport const title: PanelTokenSections.Title = {\n    fontWeight: '700'\n};\n\nexport const content: PanelTokenSections.Content = {\n    padding: '1.125rem'\n};\n\nexport const footer: PanelTokenSections.Footer = {\n    padding: '1.125rem'\n};\n\nexport const colorScheme: PanelTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    toggleableHeader,\n    title,\n    content,\n    footer,\n    colorScheme\n} satisfies PanelDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAgC;AAAA,EACzC,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,OAAO;AACX;AAEO,IAAM,SAAoC;AAAA,EAC7C,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,mBAAwD;AAAA,EACjE,SAAS;AACb;AAEO,IAAM,QAAkC;AAAA,EAC3C,YAAY;AAChB;AAEO,IAAM,UAAsC;AAAA,EAC/C,SAAS;AACb;AAEO,IAAM,SAAoC;AAAA,EAC7C,SAAS;AACb;AAEO,IAAM,cAA8C;AAAA,EACvD,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAO,gBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}