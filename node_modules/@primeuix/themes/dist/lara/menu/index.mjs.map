{"version": 3, "sources": ["../../../src/presets/lara/menu/index.ts"], "sourcesContent": ["import type { MenuDesignTokens, MenuTokenSections } from '@primeuix/themes/types/menu';\n\nexport const root: MenuTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    shadow: '{overlay.navigation.shadow}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const list: MenuTokenSections.List = {\n    padding: '{navigation.list.padding}',\n    gap: '{navigation.list.gap}'\n};\n\nexport const item: MenuTokenSections.Item = {\n    focusBackground: '{navigation.item.focus.background}',\n    color: '{navigation.item.color}',\n    focusColor: '{navigation.item.focus.color}',\n    padding: '{navigation.item.padding}',\n    borderRadius: '{navigation.item.border.radius}',\n    gap: '{navigation.item.gap}',\n    icon: {\n        color: '{navigation.item.icon.color}',\n        focusColor: '{navigation.item.icon.focus.color}'\n    }\n};\n\nexport const submenuLabel: MenuTokenSections.SubmenuLabel = {\n    padding: '{navigation.submenu.label.padding}',\n    fontWeight: '{navigation.submenu.label.font.weight}',\n    background: '{navigation.submenu.label.background}',\n    color: '{navigation.submenu.label.color}'\n};\n\nexport const separator: MenuTokenSections.Separator = {\n    borderColor: '{content.border.color}'\n};\n\nexport default {\n    root,\n    list,\n    item,\n    submenuLabel,\n    separator\n} satisfies MenuDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAA+B;AAAA,EACxC,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,oBAAoB;AACxB;AAEO,IAAM,OAA+B;AAAA,EACxC,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,OAA+B;AAAA,EACxC,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AAAA,EACL,MAAM;AAAA,IACF,OAAO;AAAA,IACP,YAAY;AAAA,EAChB;AACJ;AAEO,IAAM,eAA+C;AAAA,EACxD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AACX;AAEO,IAAM,YAAyC;AAAA,EAClD,aAAa;AACjB;AAEA,IAAO,eAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}