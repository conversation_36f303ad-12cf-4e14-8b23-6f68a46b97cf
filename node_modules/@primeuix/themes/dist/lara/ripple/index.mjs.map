{"version": 3, "sources": ["../../../src/presets/lara/ripple/index.ts"], "sourcesContent": ["import type { RippleDesignTokens, RippleTokenSections } from '@primeuix/themes/types/ripple';\n\nexport const colorScheme: RippleTokenSections.ColorScheme = {\n    light: {\n        root: {\n            background: 'rgba(0,0,0,0.1)'\n        }\n    },\n    dark: {\n        root: {\n            background: 'rgba(255,255,255,0.3)'\n        }\n    }\n};\n\nexport default {\n    colorScheme\n} satisfies RippleDesignTokens;\n"], "mappings": ";AAEO,IAAM,cAA+C;AAAA,EACxD,OAAO;AAAA,IACH,MAAM;AAAA,MACF,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,MACF,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,IAAO,iBAAQ;AAAA,EACX;AACJ;", "names": []}