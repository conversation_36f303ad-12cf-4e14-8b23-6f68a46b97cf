{"version": 3, "sources": ["../../../src/presets/lara/fileupload/index.ts"], "sourcesContent": ["import type { FileUploadDesignTokens, FileUploadTokenSections } from '@primeuix/themes/types/fileupload';\n\nexport const root: FileUploadTokenSections.Root = {\n    background: '{content.background}',\n    borderColor: '{content.border.color}',\n    color: '{content.color}',\n    borderRadius: '{content.border.radius}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const header: FileUploadTokenSections.Header = {\n    borderWidth: '0 0 1px 0',\n    borderColor: '{content.border.color}',\n    padding: '1.125rem',\n    borderRadius: '5px 5px 0 0',\n    gap: '0.5rem'\n};\n\nexport const content: FileUploadTokenSections.Content = {\n    highlightBorderColor: '{primary.color}',\n    padding: '1.125rem',\n    gap: '1rem'\n};\n\nexport const file: FileUploadTokenSections.File = {\n    padding: '1rem',\n    gap: '1rem',\n    borderColor: '{content.border.color}',\n    info: {\n        gap: '0.5rem'\n    }\n};\n\nexport const fileList: FileUploadTokenSections.FileList = {\n    gap: '0.5rem'\n};\n\nexport const progressbar: FileUploadTokenSections.Progressbar = {\n    height: '0.25rem'\n};\n\nexport const basic: FileUploadTokenSections.Basic = {\n    gap: '0.5rem'\n};\n\nexport const colorScheme: FileUploadTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    content,\n    file,\n    fileList,\n    progressbar,\n    basic,\n    colorScheme\n} satisfies FileUploadDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAqC;AAAA,EAC9C,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,EACd,oBAAoB;AACxB;AAEO,IAAM,SAAyC;AAAA,EAClD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,KAAK;AACT;AAEO,IAAM,UAA2C;AAAA,EACpD,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,KAAK;AACT;AAEO,IAAM,OAAqC;AAAA,EAC9C,SAAS;AAAA,EACT,KAAK;AAAA,EACL,aAAa;AAAA,EACb,MAAM;AAAA,IACF,KAAK;AAAA,EACT;AACJ;AAEO,IAAM,WAA6C;AAAA,EACtD,KAAK;AACT;AAEO,IAAM,cAAmD;AAAA,EAC5D,QAAQ;AACZ;AAEO,IAAM,QAAuC;AAAA,EAChD,KAAK;AACT;AAEO,IAAM,cAAmD;AAAA,EAC5D,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAO,qBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}