var r={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},o={borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",padding:"1.125rem",borderRadius:"5px 5px 0 0",gap:"0.5rem"},e={highlightBorderColor:"{primary.color}",padding:"1.125rem",gap:"1rem"},a={padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},t={gap:"0.5rem"},d={height:"0.25rem"},n={gap:"0.5rem"},c={light:{header:{background:"{surface.50}",color:"{text.color}"}},dark:{header:{background:"{surface.800}",color:"{text.color}"}}},i={root:r,header:o,content:e,file:a,fileList:t,progressbar:d,basic:n,colorScheme:c};export{n as basic,c as colorScheme,e as content,i as default,a as file,t as fileList,o as header,d as progressbar,r as root};//# sourceMappingURL=index.mjs.map