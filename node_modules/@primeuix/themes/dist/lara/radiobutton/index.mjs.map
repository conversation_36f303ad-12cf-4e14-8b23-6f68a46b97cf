{"version": 3, "sources": ["../../../src/presets/lara/radiobutton/index.ts"], "sourcesContent": ["import type { RadioButtonDesignTokens, RadioButtonTokenSections } from '@primeuix/themes/types/radiobutton';\n\nexport const root: RadioButtonTokenSections.Root = {\n    width: '1.5rem',\n    height: '1.5rem',\n    background: '{form.field.background}',\n    checkedBackground: '{primary.color}',\n    checkedHoverBackground: '{primary.hover.color}',\n    disabledBackground: '{form.field.disabled.background}',\n    filledBackground: '{form.field.filled.background}',\n    borderColor: '{form.field.border.color}',\n    hoverBorderColor: '{form.field.hover.border.color}',\n    focusBorderColor: '{form.field.focus.border.color}',\n    checkedBorderColor: '{primary.color}',\n    checkedHoverBorderColor: '{primary.hover.color}',\n    checkedFocusBorderColor: '{primary.color}',\n    checkedDisabledBorderColor: '{form.field.border.color}',\n    invalidBorderColor: '{form.field.invalid.border.color}',\n    shadow: '{form.field.shadow}',\n    focusRing: {\n        width: '{form.field.focus.ring.width}',\n        style: '{form.field.focus.ring.style}',\n        color: '{form.field.focus.ring.color}',\n        offset: '{form.field.focus.ring.offset}',\n        shadow: '{form.field.focus.ring.shadow}'\n    },\n    transitionDuration: '{form.field.transition.duration}',\n    sm: {\n        width: '1.25rem',\n        height: '1.25rem'\n    },\n    lg: {\n        width: '1.75rem',\n        height: '1.75rem'\n    }\n};\n\nexport const icon: RadioButtonTokenSections.Icon = {\n    size: '1rem',\n    checkedColor: '{primary.contrast.color}',\n    checkedHoverColor: '{primary.contrast.color}',\n    disabledColor: '{form.field.disabled.color}',\n    sm: {\n        size: '0.75rem'\n    },\n    lg: {\n        size: '1.25rem'\n    }\n};\n\nexport default {\n    root,\n    icon\n} satisfies RadioButtonDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAsC;AAAA,EAC/C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,oBAAoB;AAAA,EACpB,IAAI;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,IAAI;AAAA,IACA,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,OAAsC;AAAA,EAC/C,MAAM;AAAA,EACN,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,IAAI;AAAA,IACA,MAAM;AAAA,EACV;AAAA,EACA,IAAI;AAAA,IACA,MAAM;AAAA,EACV;AACJ;AAEA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}