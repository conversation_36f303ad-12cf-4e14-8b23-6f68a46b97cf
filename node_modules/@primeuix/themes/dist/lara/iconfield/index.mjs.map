{"version": 3, "sources": ["../../../src/presets/lara/iconfield/index.ts"], "sourcesContent": ["import type { IconFieldDesignTokens, IconFieldTokenSections } from '@primeuix/themes/types/iconfield';\n\nexport const icon: IconFieldTokenSections.Icon = {\n    color: '{form.field.icon.color}'\n};\n\nexport default {\n    icon\n} satisfies IconFieldDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAoC;AAAA,EAC7C,OAAO;AACX;AAEA,IAAO,oBAAQ;AAAA,EACX;AACJ;", "names": []}