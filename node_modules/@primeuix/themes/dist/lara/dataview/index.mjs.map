{"version": 3, "sources": ["../../../src/presets/lara/dataview/index.ts"], "sourcesContent": ["import type { DataViewDesignTokens, DataViewTokenSections } from '@primeuix/themes/types/dataview';\n\nexport const root: DataViewTokenSections.Root = {\n    borderColor: '{content.border.color}',\n    borderWidth: '1px',\n    borderRadius: '4px',\n    padding: '0'\n};\n\nexport const header: DataViewTokenSections.Header = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0',\n    padding: '0.875rem 1.125rem',\n    borderRadius: '5px 5px 0 0'\n};\n\nexport const content: DataViewTokenSections.Content = {\n    background: '{content.background}',\n    color: '{content.color}',\n    borderColor: 'transparent',\n    borderWidth: '0',\n    padding: '0',\n    borderRadius: '5px'\n};\n\nexport const footer: DataViewTokenSections.Footer = {\n    background: '{content.background}',\n    color: '{content.color}',\n    borderColor: '{content.border.color}',\n    borderWidth: '1px 0 0 0',\n    padding: '0.875rem 1.125rem',\n    borderRadius: '0 0 5px 5px'\n};\n\nexport const paginatorTop: DataViewTokenSections.PaginatorTop = {\n    borderColor: '{content.border.color}',\n    borderWidth: '0 0 1px 0'\n};\n\nexport const paginatorBottom: DataViewTokenSections.PaginatorBottom = {\n    borderColor: '{content.border.color}',\n    borderWidth: '1px 0 0 0'\n};\n\nexport const colorScheme: DataViewTokenSections.ColorScheme = {\n    light: {\n        header: {\n            background: '{surface.50}',\n            color: '{text.color}'\n        }\n    },\n    dark: {\n        header: {\n            background: '{surface.800}',\n            color: '{text.color}'\n        }\n    }\n};\n\nexport default {\n    root,\n    header,\n    content,\n    footer,\n    paginatorTop,\n    paginatorBottom,\n    colorScheme\n} satisfies DataViewDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,SAAS;AACb;AAEO,IAAM,SAAuC;AAAA,EAChD,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,UAAyC;AAAA,EAClD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,SAAuC;AAAA,EAChD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAClB;AAEO,IAAM,eAAmD;AAAA,EAC5D,aAAa;AAAA,EACb,aAAa;AACjB;AAEO,IAAM,kBAAyD;AAAA,EAClE,aAAa;AAAA,EACb,aAAa;AACjB;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,QAAQ;AAAA,MACJ,YAAY;AAAA,MACZ,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}