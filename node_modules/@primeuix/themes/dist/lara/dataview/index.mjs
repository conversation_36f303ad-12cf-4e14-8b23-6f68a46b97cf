var o={borderColor:"{content.border.color}",borderWidth:"1px",borderRadius:"4px",padding:"0"},r={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.875rem 1.125rem",borderRadius:"5px 5px 0 0"},e={background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"5px"},d={background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.875rem 1.125rem",borderRadius:"0 0 5px 5px"},t={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},n={borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"},c={light:{header:{background:"{surface.50}",color:"{text.color}"}},dark:{header:{background:"{surface.800}",color:"{text.color}"}}},a={root:o,header:r,content:e,footer:d,paginatorTop:t,paginatorBottom:n,colorScheme:c};export{c as colorScheme,e as content,a as default,d as footer,r as header,n as paginatorBottom,t as paginatorTop,o as root};//# sourceMappingURL=index.mjs.map