{"version": 3, "sources": ["../../../src/presets/lara/picklist/index.ts"], "sourcesContent": ["import type { PickListDesignTokens, PickListTokenSections } from '@primeuix/themes/types/picklist';\n\nexport const root: PickListTokenSections.Root = {\n    gap: '1.125rem'\n};\n\nexport const controls: PickListTokenSections.Controls = {\n    gap: '0.5rem'\n};\n\nexport default {\n    root,\n    controls\n} satisfies PickListDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,KAAK;AACT;AAEO,IAAM,WAA2C;AAAA,EACpD,KAAK;AACT;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AACJ;", "names": []}