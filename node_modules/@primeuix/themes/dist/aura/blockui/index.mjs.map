{"version": 3, "sources": ["../../../src/presets/aura/blockui/index.ts"], "sourcesContent": ["import type { BlockUIDesignTokens, BlockUITokenSections } from '@primeuix/themes/types/blockui';\n\nexport const root: BlockUITokenSections.Root = {\n    borderRadius: '{content.border.radius}'\n};\n\nexport default {\n    root\n} satisfies BlockUIDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAkC;AAAA,EAC3C,cAAc;AAClB;AAEA,IAAO,kBAAQ;AAAA,EACX;AACJ;", "names": []}