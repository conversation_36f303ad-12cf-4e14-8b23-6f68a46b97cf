var o={background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",shadow:"{overlay.modal.shadow}"},a={padding:"{overlay.modal.padding}"},d={fontSize:"1.5rem",fontWeight:"600"},r={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},l={padding:"{overlay.modal.padding}"},e={root:o,header:a,title:d,content:r,footer:l};export{r as content,e as default,l as footer,a as header,o as root,d as title};//# sourceMappingURL=index.mjs.map