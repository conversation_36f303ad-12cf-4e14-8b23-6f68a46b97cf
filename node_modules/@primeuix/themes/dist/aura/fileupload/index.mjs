var r={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},o={background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"unset",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},e={highlightBorderColor:"{primary.color}",padding:"0 1.125rem 1.125rem 1.125rem",gap:"1rem"},t={padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},a={gap:"0.5rem"},n={height:"0.25rem"},d={gap:"0.5rem"},i={root:r,header:o,content:e,file:t,fileList:a,progressbar:n,basic:d};export{d as basic,e as content,i as default,t as file,a as fileList,o as header,n as progressbar,r as root};//# sourceMappingURL=index.mjs.map