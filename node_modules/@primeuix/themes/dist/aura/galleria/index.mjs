var o={borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},r={background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},e={size:"1.5rem"},t={background:"{content.background}",padding:"1rem 0.25rem"},c={size:"2rem",borderRadius:"{content.border.radius}",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},n={size:"1rem"},a={background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},s={gap:"0.5rem",padding:"1rem"},u={width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},i={background:"rgba(0, 0, 0, 0.5)"},d={background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},g={size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},f={size:"1.5rem"},h={light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}},l={root:o,navButton:r,navIcon:e,thumbnailsContent:t,thumbnailNavButton:c,thumbnailNavButtonIcon:n,caption:a,indicatorList:s,indicatorButton:u,insetIndicatorList:i,insetIndicatorButton:d,closeButton:g,closeButtonIcon:f,colorScheme:h};export{a as caption,g as closeButton,f as closeButtonIcon,h as colorScheme,l as default,u as indicatorButton,s as indicatorList,d as insetIndicatorButton,i as insetIndicatorList,r as navButton,e as navIcon,o as root,c as thumbnailNavButton,n as thumbnailNavButtonIcon,t as thumbnailsContent};//# sourceMappingURL=index.mjs.map