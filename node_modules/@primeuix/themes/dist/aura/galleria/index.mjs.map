{"version": 3, "sources": ["../../../src/presets/aura/galleria/index.ts"], "sourcesContent": ["import type { GalleriaDesignTokens, GalleriaTokenSections } from '@primeuix/themes/types/galleria';\n\nexport const root: GalleriaTokenSections.Root = {\n    borderWidth: '1px',\n    borderColor: '{content.border.color}',\n    borderRadius: '{content.border.radius}',\n    transitionDuration: '{transition.duration}'\n};\n\nexport const navButton: GalleriaTokenSections.NavButton = {\n    background: 'rgba(255, 255, 255, 0.1)',\n    hoverBackground: 'rgba(255, 255, 255, 0.2)',\n    color: '{surface.100}',\n    hoverColor: '{surface.0}',\n    size: '3rem',\n    gutter: '0.5rem',\n    prev: {\n        borderRadius: '50%'\n    },\n    next: {\n        borderRadius: '50%'\n    },\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const navIcon: GalleriaTokenSections.NavIcon = {\n    size: '1.5rem'\n};\n\nexport const thumbnailsContent: GalleriaTokenSections.ThumbnailsContent = {\n    background: '{content.background}',\n    padding: '1rem 0.25rem'\n};\n\nexport const thumbnailNavButton: GalleriaTokenSections.ThumbnailNavButton = {\n    size: '2rem',\n    borderRadius: '{content.border.radius}',\n    gutter: '0.5rem',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const thumbnailNavButtonIcon: GalleriaTokenSections.ThumbnailNavButtonIcon = {\n    size: '1rem'\n};\n\nexport const caption: GalleriaTokenSections.Caption = {\n    background: 'rgba(0, 0, 0, 0.5)',\n    color: '{surface.100}',\n    padding: '1rem'\n};\n\nexport const indicatorList: GalleriaTokenSections.IndicatorList = {\n    gap: '0.5rem',\n    padding: '1rem'\n};\n\nexport const indicatorButton: GalleriaTokenSections.IndicatorButton = {\n    width: '1rem',\n    height: '1rem',\n    activeBackground: '{primary.color}',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const insetIndicatorList: GalleriaTokenSections.InsetIndicatorList = {\n    background: 'rgba(0, 0, 0, 0.5)'\n};\n\nexport const insetIndicatorButton: GalleriaTokenSections.InsetIndicatorButton = {\n    background: 'rgba(255, 255, 255, 0.4)',\n    hoverBackground: 'rgba(255, 255, 255, 0.6)',\n    activeBackground: 'rgba(255, 255, 255, 0.9)'\n};\n\nexport const closeButton: GalleriaTokenSections.CloseButton = {\n    size: '3rem',\n    gutter: '0.5rem',\n    background: 'rgba(255, 255, 255, 0.1)',\n    hoverBackground: 'rgba(255, 255, 255, 0.2)',\n    color: '{surface.50}',\n    hoverColor: '{surface.0}',\n    borderRadius: '50%',\n    focusRing: {\n        width: '{focus.ring.width}',\n        style: '{focus.ring.style}',\n        color: '{focus.ring.color}',\n        offset: '{focus.ring.offset}',\n        shadow: '{focus.ring.shadow}'\n    }\n};\n\nexport const closeButtonIcon: GalleriaTokenSections.CloseButtonIcon = {\n    size: '1.5rem'\n};\n\nexport const colorScheme: GalleriaTokenSections.ColorScheme = {\n    light: {\n        thumbnailNavButton: {\n            hoverBackground: '{surface.100}',\n            color: '{surface.600}',\n            hoverColor: '{surface.700}'\n        },\n        indicatorButton: {\n            background: '{surface.200}',\n            hoverBackground: '{surface.300}'\n        }\n    },\n    dark: {\n        thumbnailNavButton: {\n            hoverBackground: '{surface.700}',\n            color: '{surface.400}',\n            hoverColor: '{surface.0}'\n        },\n        indicatorButton: {\n            background: '{surface.700}',\n            hoverBackground: '{surface.600}'\n        }\n    }\n};\n\nexport default {\n    root,\n    navButton,\n    navIcon,\n    thumbnailsContent,\n    thumbnailNavButton,\n    thumbnailNavButtonIcon,\n    caption,\n    indicatorList,\n    indicatorButton,\n    insetIndicatorList,\n    insetIndicatorButton,\n    closeButton,\n    closeButtonIcon,\n    colorScheme\n} satisfies GalleriaDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAmC;AAAA,EAC5C,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc;AAAA,EACd,oBAAoB;AACxB;AAEO,IAAM,YAA6C;AAAA,EACtD,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,IACF,cAAc;AAAA,EAClB;AAAA,EACA,MAAM;AAAA,IACF,cAAc;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,UAAyC;AAAA,EAClD,MAAM;AACV;AAEO,IAAM,oBAA6D;AAAA,EACtE,YAAY;AAAA,EACZ,SAAS;AACb;AAEO,IAAM,qBAA+D;AAAA,EACxE,MAAM;AAAA,EACN,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,yBAAuE;AAAA,EAChF,MAAM;AACV;AAEO,IAAM,UAAyC;AAAA,EAClD,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AACb;AAEO,IAAM,gBAAqD;AAAA,EAC9D,KAAK;AAAA,EACL,SAAS;AACb;AAEO,IAAM,kBAAyD;AAAA,EAClE,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,qBAA+D;AAAA,EACxE,YAAY;AAChB;AAEO,IAAM,uBAAmE;AAAA,EAC5E,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,kBAAkB;AACtB;AAEO,IAAM,cAAiD;AAAA,EAC1D,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,kBAAyD;AAAA,EAClE,MAAM;AACV;AAEO,IAAM,cAAiD;AAAA,EAC1D,OAAO;AAAA,IACH,oBAAoB;AAAA,MAChB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,IACA,iBAAiB;AAAA,MACb,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF,oBAAoB;AAAA,MAChB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,IACA,iBAAiB;AAAA,MACb,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACrB;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}