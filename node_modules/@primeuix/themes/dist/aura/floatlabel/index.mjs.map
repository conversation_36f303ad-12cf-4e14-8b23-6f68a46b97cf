{"version": 3, "sources": ["../../../src/presets/aura/floatlabel/index.ts"], "sourcesContent": ["import type { FloatLabelDesignTokens, FloatLabelTokenSections } from '@primeuix/themes/types/floatlabel';\n\nexport const root: FloatLabelTokenSections.Root = {\n    color: '{form.field.float.label.color}',\n    focusColor: '{form.field.float.label.focus.color}',\n    activeColor: '{form.field.float.label.active.color}',\n    invalidColor: '{form.field.float.label.invalid.color}',\n    transitionDuration: '0.2s',\n    positionX: '{form.field.padding.x}',\n    positionY: '{form.field.padding.y}',\n    fontWeight: '500',\n    active: {\n        fontSize: '0.75rem',\n        fontWeight: '400'\n    }\n};\n\nexport const over: FloatLabelTokenSections.Over = {\n    active: {\n        top: '-1.25rem'\n    }\n};\n\nexport const inside: FloatLabelTokenSections.In = {\n    input: {\n        paddingTop: '1.5rem',\n        paddingBottom: '{form.field.padding.y}'\n    },\n    active: {\n        top: '{form.field.padding.y}'\n    }\n};\n\nexport const on: FloatLabelTokenSections.On = {\n    borderRadius: '{border.radius.xs}',\n    active: {\n        background: '{form.field.background}',\n        padding: '0 0.125rem'\n    }\n};\n\nexport default {\n    root,\n    over,\n    in: inside,\n    on\n} satisfies FloatLabelDesignTokens;\n"], "mappings": ";AAEO,IAAM,OAAqC;AAAA,EAC9C,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,QAAQ;AAAA,IACJ,UAAU;AAAA,IACV,YAAY;AAAA,EAChB;AACJ;AAEO,IAAM,OAAqC;AAAA,EAC9C,QAAQ;AAAA,IACJ,KAAK;AAAA,EACT;AACJ;AAEO,IAAM,SAAqC;AAAA,EAC9C,OAAO;AAAA,IACH,YAAY;AAAA,IACZ,eAAe;AAAA,EACnB;AAAA,EACA,QAAQ;AAAA,IACJ,KAAK;AAAA,EACT;AACJ;AAEO,IAAM,KAAiC;AAAA,EAC1C,cAAc;AAAA,EACd,QAAQ;AAAA,IACJ,YAAY;AAAA,IACZ,SAAS;AAAA,EACb;AACJ;AAEA,IAAO,qBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ;AACJ;", "names": []}