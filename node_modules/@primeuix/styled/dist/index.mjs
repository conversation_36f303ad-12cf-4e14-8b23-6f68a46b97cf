var rt=Object.defineProperty,st=Object.defineProperties;var nt=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,be=Object.prototype.propertyIsEnumerable;var _e=(e,t,r)=>t in e?rt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,h=(e,t)=>{for(var r in t||(t={}))xe.call(t,r)&&_e(e,r,t[r]);if(F)for(var r of F(t))be.call(t,r)&&_e(e,r,t[r]);return e},$=(e,t)=>st(e,nt(t));var v=(e,t)=>{var r={};for(var s in e)xe.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(e!=null&&F)for(var s of F(e))t.indexOf(s)<0&&be.call(e,s)&&(r[s]=e[s]);return r};import{deepMerge as it}from"@primeuix/utils/object";function ke(...e){return it(...e)}import{deepMerge as $t}from"@primeuix/utils/object";import{EventBus as ot}from"@primeuix/utils/eventbus";var at=ot(),N=at;import{getKeyValue as lt,isArray as ct,isNotEmpty as mt,isNumber as se,isObject as $e,isString as P,matchRegex as J,toKebabCase as ut}from"@primeuix/utils/object";var k=/{([^}]*)}/g,ne=/(\d+\s+[\+\-\*\/]\s+\d+)/g,ie=/var\([^)]+\)/g;function oe(e){return P(e)?e.replace(/[A-Z]/g,(t,r)=>r===0?t:"."+t.toLowerCase()).toLowerCase():e}function Lt(e,t){ct(e)?e.push(...t||[]):$e(e)&&Object.assign(e,t)}function ve(e){return $e(e)&&e.hasOwnProperty("$value")&&e.hasOwnProperty("$type")?e.$value:e}function At(e,t=""){return["opacity","z-index","line-height","font-weight","flex","flex-grow","flex-shrink","order"].some(s=>t.endsWith(s))?e:`${e}`.trim().split(" ").map(a=>se(a)?`${a}px`:a).join(" ")}function dt(e){return e.replaceAll(/ /g,"").replace(/[^\w]/g,"-")}function Q(e="",t=""){return dt(`${P(e,!1)&&P(t,!1)?`${e}-`:e}${t}`)}function ae(e="",t=""){return`--${Q(e,t)}`}function ht(e=""){let t=(e.match(/{/g)||[]).length,r=(e.match(/}/g)||[]).length;return(t+r)%2!==0}function Y(e,t="",r="",s=[],i){if(P(e)){let a=e.trim();if(ht(a))return;if(J(a,k)){let n=a.replaceAll(k,l=>{let c=l.replace(/{|}/g,"").split(".").filter(m=>!s.some(d=>J(m,d)));return`var(${ae(r,ut(c.join("-")))}${mt(i)?`, ${i}`:""})`});return J(n.replace(ie,"0"),ne)?`calc(${n})`:n}return a}else if(se(e))return e}function Dt(e={},t){if(P(t)){let r=t.trim();return J(r,k)?r.replaceAll(k,s=>lt(e,s.replace(/{|}/g,""))):r}else if(se(t))return t}function Re(e,t,r){P(t,!1)&&e.push(`${t}:${r};`)}function C(e,t){return e?`${e}{${t}}`:""}function le(e,t){if(e.indexOf("dt(")===-1)return e;function r(n,l){let o=[],c=0,m="",d=null,u=0;for(;c<=n.length;){let g=n[c];if((g==='"'||g==="'"||g==="`")&&n[c-1]!=="\\"&&(d=d===g?null:g),!d&&(g==="("&&u++,g===")"&&u--,(g===","||c===n.length)&&u===0)){let f=m.trim();f.startsWith("dt(")?o.push(le(f,l)):o.push(s(f)),m="",c++;continue}g!==void 0&&(m+=g),c++}return o}function s(n){let l=n[0];if((l==='"'||l==="'"||l==="`")&&n[n.length-1]===l)return n.slice(1,-1);let o=Number(n);return isNaN(o)?n:o}let i=[],a=[];for(let n=0;n<e.length;n++)if(e[n]==="d"&&e.slice(n,n+3)==="dt(")a.push(n),n+=2;else if(e[n]===")"&&a.length>0){let l=a.pop();a.length===0&&i.push([l,n])}if(!i.length)return e;for(let n=i.length-1;n>=0;n--){let[l,o]=i[n],c=e.slice(l+3,o),m=r(c,t),d=t(...m);e=e.slice(0,l)+d+e.slice(o+1)}return e}import{isEmpty as kt,isNotEmpty as _,isObject as he,matchRegex as we,minifyCSS as Oe,resolve as ee}from"@primeuix/utils/object";function Te(e){return e.length===4?`#${e[1]}${e[1]}${e[2]}${e[2]}${e[3]}${e[3]}`:e}function Ne(e){let t=parseInt(e.substring(1),16),r=t>>16&255,s=t>>8&255,i=t&255;return{r,g:s,b:i}}function gt(e,t,r){return`#${e.toString(16).padStart(2,"0")}${t.toString(16).padStart(2,"0")}${r.toString(16).padStart(2,"0")}`}var D=(e,t,r)=>{e=Te(e),t=Te(t);let a=(r/100*2-1+1)/2,n=1-a,l=Ne(e),o=Ne(t),c=Math.round(l.r*a+o.r*n),m=Math.round(l.g*a+o.g*n),d=Math.round(l.b*a+o.b*n);return gt(c,m,d)};import{matchRegex as pt}from"@primeuix/utils";var ce=(e,t)=>D("#000000",e,t);var me=(e,t)=>D("#ffffff",e,t);var Ce=[50,100,200,300,400,500,600,700,800,900,950],ft=e=>{if(pt(e,k)){let t=e.replace(/{|}/g,"");return Ce.reduce((r,s)=>(r[s]=`{${t}.${s}}`,r),{})}return typeof e=="string"?Ce.reduce((t,r,s)=>(t[r]=s<=5?me(e,(5-s)*19):ce(e,(s-5)*15),t),{}):e};import{resolve as Ee}from"@primeuix/utils";import{isEmpty as yt,matchRegex as St}from"@primeuix/utils/object";var rr=e=>{var a;let t=S.getTheme(),r=ue(t,e,void 0,"variable"),s=(a=r==null?void 0:r.match(/--[\w-]+/g))==null?void 0:a[0],i=ue(t,e,void 0,"value");return{name:s,variable:r,value:i}},E=(...e)=>ue(S.getTheme(),...e),ue=(e={},t,r,s)=>{if(t){let{variable:i,options:a}=S.defaults||{},{prefix:n,transform:l}=(e==null?void 0:e.options)||a||{},o=St(t,k)?t:`{${t}}`;return s==="value"||yt(s)&&l==="strict"?S.getTokenValue(t):Y(o,void 0,n,[i.excludedKeyRegex],r)}return""};function ar(e,...t){if(e instanceof Array){let r=e.reduce((s,i,a)=>{var n;return s+i+((n=Ee(t[a],{dt:E}))!=null?n:"")},"");return le(r,E)}return Ee(e,{dt:E})}import{mergeKeys as Pe}from"@primeuix/utils/object";var w=(e={})=>{let{preset:t,options:r}=e;return{preset(s){return t=t?Pe(t,s):s,this},options(s){return r=r?h(h({},r),s):s,this},primaryPalette(s){let{semantic:i}=t||{};return t=$(h({},t),{semantic:$(h({},i),{primary:s})}),this},surfacePalette(s){var o,c;let{semantic:i}=t||{},a=s&&Object.hasOwn(s,"light")?s.light:s,n=s&&Object.hasOwn(s,"dark")?s.dark:s,l={colorScheme:{light:h(h({},(o=i==null?void 0:i.colorScheme)==null?void 0:o.light),!!a&&{surface:a}),dark:h(h({},(c=i==null?void 0:i.colorScheme)==null?void 0:c.dark),!!n&&{surface:n})}};return t=$(h({},t),{semantic:h(h({},i),l)}),this},define({useDefaultPreset:s=!1,useDefaultOptions:i=!1}={}){return{preset:s?S.getPreset():t,options:i?S.getOptions():r}},update({mergePresets:s=!0,mergeOptions:i=!0}={}){let a={preset:s?Pe(S.getPreset(),t):t,options:i?h(h({},S.getOptions()),r):r};return S.setTheme(a),a},use(s){let i=this.define(s);return S.setTheme(i),i}}};import{isObject as _t,matchRegex as xt,toKebabCase as bt}from"@primeuix/utils/object";function de(e,t={}){let r=S.defaults.variable,{prefix:s=r.prefix,selector:i=r.selector,excludedKeyRegex:a=r.excludedKeyRegex}=t,n=[],l=[],o=[{node:e,path:s}];for(;o.length;){let{node:m,path:d}=o.pop();for(let u in m){let g=m[u],f=ve(g),p=xt(u,a)?Q(d):Q(d,bt(u));if(_t(f))o.push({node:f,path:p});else{let y=ae(p),R=Y(f,p,s,[a]);Re(l,y,R);let T=p;s&&T.startsWith(s+"-")&&(T=T.slice(s.length+1)),n.push(T.replace(/-/g,"."))}}}let c=l.join("");return{value:l,tokens:n,declarations:c,css:C(i,c)}}var b={regex:{rules:{class:{pattern:/^\.([a-zA-Z][\w-]*)$/,resolve(e){return{type:"class",selector:e,matched:this.pattern.test(e.trim())}}},attr:{pattern:/^\[(.*)\]$/,resolve(e){return{type:"attr",selector:`:root${e},:host${e}`,matched:this.pattern.test(e.trim())}}},media:{pattern:/^@media (.*)$/,resolve(e){return{type:"media",selector:e,matched:this.pattern.test(e.trim())}}},system:{pattern:/^system$/,resolve(e){return{type:"system",selector:"@media (prefers-color-scheme: dark)",matched:this.pattern.test(e.trim())}}},custom:{resolve(e){return{type:"custom",selector:e,matched:!0}}}},resolve(e){let t=Object.keys(this.rules).filter(r=>r!=="custom").map(r=>this.rules[r]);return[e].flat().map(r=>{var s;return(s=t.map(i=>i.resolve(r)).find(i=>i.matched))!=null?s:this.rules.custom.resolve(r)})}},_toVariables(e,t){return de(e,{prefix:t==null?void 0:t.prefix})},getCommon({name:e="",theme:t={},params:r,set:s,defaults:i}){var R,T,j,O,M,z,V;let{preset:a,options:n}=t,l,o,c,m,d,u,g;if(_(a)&&n.transform!=="strict"){let{primitive:L,semantic:te,extend:re}=a,f=te||{},{colorScheme:K}=f,A=v(f,["colorScheme"]),x=re||{},{colorScheme:X}=x,G=v(x,["colorScheme"]),p=K||{},{dark:U}=p,B=v(p,["dark"]),y=X||{},{dark:I}=y,H=v(y,["dark"]),W=_(L)?this._toVariables({primitive:L},n):{},q=_(A)?this._toVariables({semantic:A},n):{},Z=_(B)?this._toVariables({light:B},n):{},pe=_(U)?this._toVariables({dark:U},n):{},fe=_(G)?this._toVariables({semantic:G},n):{},ye=_(H)?this._toVariables({light:H},n):{},Se=_(I)?this._toVariables({dark:I},n):{},[Me,ze]=[(R=W.declarations)!=null?R:"",W.tokens],[Ke,Xe]=[(T=q.declarations)!=null?T:"",q.tokens||[]],[Ge,Ue]=[(j=Z.declarations)!=null?j:"",Z.tokens||[]],[Be,Ie]=[(O=pe.declarations)!=null?O:"",pe.tokens||[]],[He,We]=[(M=fe.declarations)!=null?M:"",fe.tokens||[]],[qe,Ze]=[(z=ye.declarations)!=null?z:"",ye.tokens||[]],[Fe,Je]=[(V=Se.declarations)!=null?V:"",Se.tokens||[]];l=this.transformCSS(e,Me,"light","variable",n,s,i),o=ze;let Qe=this.transformCSS(e,`${Ke}${Ge}`,"light","variable",n,s,i),Ye=this.transformCSS(e,`${Be}`,"dark","variable",n,s,i);c=`${Qe}${Ye}`,m=[...new Set([...Xe,...Ue,...Ie])];let et=this.transformCSS(e,`${He}${qe}color-scheme:light`,"light","variable",n,s,i),tt=this.transformCSS(e,`${Fe}color-scheme:dark`,"dark","variable",n,s,i);d=`${et}${tt}`,u=[...new Set([...We,...Ze,...Je])],g=ee(a.css,{dt:E})}return{primitive:{css:l,tokens:o},semantic:{css:c,tokens:m},global:{css:d,tokens:u},style:g}},getPreset({name:e="",preset:t={},options:r,params:s,set:i,defaults:a,selector:n}){var f,x,p;let l,o,c;if(_(t)&&r.transform!=="strict"){let y=e.replace("-directive",""),m=t,{colorScheme:R,extend:T,css:j}=m,O=v(m,["colorScheme","extend","css"]),d=T||{},{colorScheme:M}=d,z=v(d,["colorScheme"]),u=R||{},{dark:V}=u,L=v(u,["dark"]),g=M||{},{dark:te}=g,re=v(g,["dark"]),K=_(O)?this._toVariables({[y]:h(h({},O),z)},r):{},A=_(L)?this._toVariables({[y]:h(h({},L),re)},r):{},X=_(V)?this._toVariables({[y]:h(h({},V),te)},r):{},[G,U]=[(f=K.declarations)!=null?f:"",K.tokens||[]],[B,I]=[(x=A.declarations)!=null?x:"",A.tokens||[]],[H,W]=[(p=X.declarations)!=null?p:"",X.tokens||[]],q=this.transformCSS(y,`${G}${B}`,"light","variable",r,i,a,n),Z=this.transformCSS(y,H,"dark","variable",r,i,a,n);l=`${q}${Z}`,o=[...new Set([...U,...I,...W])],c=ee(j,{dt:E})}return{css:l,tokens:o,style:c}},getPresetC({name:e="",theme:t={},params:r,set:s,defaults:i}){var o;let{preset:a,options:n}=t,l=(o=a==null?void 0:a.components)==null?void 0:o[e];return this.getPreset({name:e,preset:l,options:n,params:r,set:s,defaults:i})},getPresetD({name:e="",theme:t={},params:r,set:s,defaults:i}){var c,m;let a=e.replace("-directive",""),{preset:n,options:l}=t,o=((c=n==null?void 0:n.components)==null?void 0:c[a])||((m=n==null?void 0:n.directives)==null?void 0:m[a]);return this.getPreset({name:a,preset:o,options:l,params:r,set:s,defaults:i})},applyDarkColorScheme(e){return!(e.darkModeSelector==="none"||e.darkModeSelector===!1)},getColorSchemeOption(e,t){var r;return this.applyDarkColorScheme(e)?this.regex.resolve(e.darkModeSelector===!0?t.options.darkModeSelector:(r=e.darkModeSelector)!=null?r:t.options.darkModeSelector):[]},getLayerOrder(e,t={},r,s){let{cssLayer:i}=t;return i?`@layer ${ee(i.order||i.name||"primeui",r)}`:""},getCommonStyleSheet({name:e="",theme:t={},params:r,props:s={},set:i,defaults:a}){let n=this.getCommon({name:e,theme:t,params:r,set:i,defaults:a}),l=Object.entries(s).reduce((o,[c,m])=>o.push(`${c}="${m}"`)&&o,[]).join(" ");return Object.entries(n||{}).reduce((o,[c,m])=>{if(he(m)&&Object.hasOwn(m,"css")){let d=Oe(m.css),u=`${c}-variables`;o.push(`<style type="text/css" data-primevue-style-id="${u}" ${l}>${d}</style>`)}return o},[]).join("")},getStyleSheet({name:e="",theme:t={},params:r,props:s={},set:i,defaults:a}){var c;let n={name:e,theme:t,params:r,set:i,defaults:a},l=(c=e.includes("-directive")?this.getPresetD(n):this.getPresetC(n))==null?void 0:c.css,o=Object.entries(s).reduce((m,[d,u])=>m.push(`${d}="${u}"`)&&m,[]).join(" ");return l?`<style type="text/css" data-primevue-style-id="${e}-variables" ${o}>${Oe(l)}</style>`:""},createTokens(e={},t,r="",s="",i={}){let a=function(l,o={},c=[]){if(c.includes(this.path))return console.warn(`Circular reference detected at ${this.path}`),{colorScheme:l,path:this.path,paths:o,value:void 0};c.push(this.path),o.name=this.path,o.binding||(o.binding={});let m=this.value;if(typeof this.value=="string"&&k.test(this.value)){let u=this.value.trim().replace(k,g=>{var y;let f=g.slice(1,-1),x=this.tokens[f];if(!x)return console.warn(`Token not found for path: ${f}`),"__UNRESOLVED__";let p=x.computed(l,o,c);return Array.isArray(p)&&p.length===2?`light-dark(${p[0].value},${p[1].value})`:(y=p==null?void 0:p.value)!=null?y:"__UNRESOLVED__"});m=ne.test(u.replace(ie,"0"))?`calc(${u})`:u}return kt(o.binding)&&delete o.binding,c.pop(),{colorScheme:l,path:this.path,paths:o,value:m.includes("__UNRESOLVED__")?void 0:m}},n=(l,o,c)=>{Object.entries(l).forEach(([m,d])=>{let u=we(m,t.variable.excludedKeyRegex)?o:o?`${o}.${oe(m)}`:oe(m),g=c?`${c}.${m}`:m;he(d)?n(d,u,g):(i[u]||(i[u]={paths:[],computed:(f,x={},p=[])=>{if(i[u].paths.length===1)return i[u].paths[0].computed(i[u].paths[0].scheme,x.binding,p);if(f&&f!=="none")for(let y=0;y<i[u].paths.length;y++){let R=i[u].paths[y];if(R.scheme===f)return R.computed(f,x.binding,p)}return i[u].paths.map(y=>y.computed(y.scheme,x[y.scheme],p))}}),i[u].paths.push({path:g,value:d,scheme:g.includes("colorScheme.light")?"light":g.includes("colorScheme.dark")?"dark":"none",computed:a,tokens:i}))})};return n(e,r,s),i},getTokenValue(e,t,r){var l;let i=(o=>o.split(".").filter(m=>!we(m.toLowerCase(),r.variable.excludedKeyRegex)).join("."))(t),a=t.includes("colorScheme.light")?"light":t.includes("colorScheme.dark")?"dark":void 0,n=[(l=e[i])==null?void 0:l.computed(a)].flat().filter(o=>o);return n.length===1?n[0].value:n.reduce((o={},c)=>{let u=c,{colorScheme:m}=u,d=v(u,["colorScheme"]);return o[m]=d,o},void 0)},getSelectorRule(e,t,r,s){return r==="class"||r==="attr"?C(_(t)?`${e}${t},${e} ${t}`:e,s):C(e,C(t!=null?t:":root,:host",s))},transformCSS(e,t,r,s,i={},a,n,l){if(_(t)){let{cssLayer:o}=i;if(s!=="style"){let c=this.getColorSchemeOption(i,n);t=r==="dark"?c.reduce((m,{type:d,selector:u})=>(_(u)&&(m+=u.includes("[CSS]")?u.replace("[CSS]",t):this.getSelectorRule(u,l,d,t)),m),""):C(l!=null?l:":root,:host",t)}if(o){let c={name:"primeui",order:"primeui"};he(o)&&(c.name=ee(o.name,{name:e,type:s})),_(c.name)&&(t=C(`@layer ${c.name}`,t),a==null||a.layerNames(c.name))}return t}return""}};var S={defaults:{variable:{prefix:"p",selector:":root,:host",excludedKeyRegex:/^(primitive|semantic|components|directives|variables|colorscheme|light|dark|common|root|states|extend|css)$/gi},options:{prefix:"p",darkModeSelector:"system",cssLayer:!1}},_theme:void 0,_layerNames:new Set,_loadedStyleNames:new Set,_loadingStyles:new Set,_tokens:{},update(e={}){let{theme:t}=e;t&&(this._theme=$(h({},t),{options:h(h({},this.defaults.options),t.options)}),this._tokens=b.createTokens(this.preset,this.defaults),this.clearLoadedStyleNames())},get theme(){return this._theme},get preset(){var e;return((e=this.theme)==null?void 0:e.preset)||{}},get options(){var e;return((e=this.theme)==null?void 0:e.options)||{}},get tokens(){return this._tokens},getTheme(){return this.theme},setTheme(e){this.update({theme:e}),N.emit("theme:change",e)},getPreset(){return this.preset},setPreset(e){this._theme=$(h({},this.theme),{preset:e}),this._tokens=b.createTokens(e,this.defaults),this.clearLoadedStyleNames(),N.emit("preset:change",e),N.emit("theme:change",this.theme)},getOptions(){return this.options},setOptions(e){this._theme=$(h({},this.theme),{options:e}),this.clearLoadedStyleNames(),N.emit("options:change",e),N.emit("theme:change",this.theme)},getLayerNames(){return[...this._layerNames]},setLayerNames(e){this._layerNames.add(e)},getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(e){return this._loadedStyleNames.has(e)},setLoadedStyleName(e){this._loadedStyleNames.add(e)},deleteLoadedStyleName(e){this._loadedStyleNames.delete(e)},clearLoadedStyleNames(){this._loadedStyleNames.clear()},getTokenValue(e){return b.getTokenValue(this.tokens,e,this.defaults)},getCommon(e="",t){return b.getCommon({name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getComponent(e="",t){let r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return b.getPresetC(r)},getDirective(e="",t){let r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return b.getPresetD(r)},getCustomPreset(e="",t,r,s){let i={name:e,preset:t,options:this.options,selector:r,params:s,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return b.getPreset(i)},getLayerOrderCSS(e=""){return b.getLayerOrder(e,this.options,{names:this.getLayerNames()},this.defaults)},transformCSS(e="",t,r="style",s){return b.transformCSS(e,t,s,r,this.options,{layerNames:this.setLayerNames.bind(this)},this.defaults)},getCommonStyleSheet(e="",t,r={}){return b.getCommonStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getStyleSheet(e,t,r={}){return b.getStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},onStyleMounted(e){this._loadingStyles.add(e)},onStyleUpdated(e){this._loadingStyles.add(e)},onStyleLoaded(e,{name:t}){this._loadingStyles.size&&(this._loadingStyles.delete(t),N.emit(`theme:${t}:load`,e),!this._loadingStyles.size&&N.emit("theme:load"))}};function Ve(...e){let t=$t(S.getPreset(),...e);return S.setPreset(t),t}function Le(e){return w().primaryPalette(e).update().preset}function Ae(e){return w().surfacePalette(e).update().preset}import{deepMerge as vt}from"@primeuix/utils/object";function De(...e){let t=vt(...e);return S.setPreset(t),t}function je(e){return w(e).update({mergePresets:!1})}import{createStyleMarkup as Rt,isNotEmpty as Tt}from"@primeuix/utils";var ge=class{constructor({attrs:t}={}){this._styles=new Map,this._attrs=t||{}}get(t){return this._styles.get(t)}has(t){return this._styles.has(t)}delete(t){this._styles.delete(t)}clear(){this._styles.clear()}add(t,r){if(Tt(r)){let s={name:t,css:r,attrs:this._attrs,markup:Rt(r,this._attrs)};this._styles.set(t,$(h({},s),{element:this.createStyleElement(s)}))}}update(){}getStyles(){return this._styles}getAllCSS(){return[...this._styles.values()].map(t=>t.css).filter(String)}getAllMarkup(){return[...this._styles.values()].map(t=>t.markup).filter(String)}getAllElements(){return[...this._styles.values()].map(t=>t.element)}createStyleElement(t={}){}},Nt=ge;export{rr as $dt,w as $t,ne as CALC_REGEX,k as EXPR_REGEX,Nt as StyleSheet,S as Theme,N as ThemeService,b as ThemeUtils,ie as VAR_REGEX,ar as css,ke as definePreset,E as dt,ue as dtwt,le as evaluateDtExpressions,Dt as getComputedValue,C as getRule,ae as getVariableName,Y as getVariableValue,ht as hasOddBraces,Lt as merge,D as mix,ft as palette,Re as setProperty,ce as shade,me as tint,dt as toNormalizePrefix,Q as toNormalizeVariable,oe as toTokenKey,At as toUnit,ve as toValue,de as toVariables,Ve as updatePreset,Le as updatePrimaryPalette,Ae as updateSurfacePalette,De as usePreset,je as useTheme};
//# sourceMappingURL=index.mjs.map