import * as _primeuix_utils_eventbus from '@primeuix/utils/eventbus';

declare function definePreset<T extends Record<string, unknown>>(...presets: T[]): T;

declare function updatePreset<T extends Record<string, unknown>>(...presets: T[]): T;

declare function updatePrimaryPalette<T = unknown, P = unknown>(palette?: T): P;

declare function updateSurfacePalette<T = unknown, P = unknown>(palette?: T): P;

declare function usePreset<T extends Record<string, unknown>>(...presets: T[]): T;

declare function useTheme<T = unknown>(theme: T): T;

declare const _default$5: {
    defaults: {
        variable: {
            prefix: string;
            selector: string;
            excludedKeyRegex: RegExp;
        };
        options: {
            prefix: string;
            darkModeSelector: string;
            cssLayer: boolean;
        };
    };
    _theme: undefined;
    _layerNames: Set<unknown>;
    _loadedStyleNames: Set<unknown>;
    _loadingStyles: Set<unknown>;
    _tokens: {};
    update(newValues?: any): void;
    readonly theme: any;
    readonly preset: any;
    readonly options: any;
    readonly tokens: {};
    getTheme(): any;
    setTheme(newValue: any): void;
    getPreset(): any;
    setPreset(newValue: any): void;
    getOptions(): any;
    setOptions(newValue: any): void;
    getLayerNames(): unknown[];
    setLayerNames(layerName: any): void;
    getLoadedStyleNames(): Set<unknown>;
    isStyleNameLoaded(name: string): boolean;
    setLoadedStyleName(name: string): void;
    deleteLoadedStyleName(name: string): void;
    clearLoadedStyleNames(): void;
    getTokenValue(tokenPath: string): any;
    getCommon(name: string | undefined, params: any): {
        primitive: {
            css: string | undefined;
            tokens: any;
        };
        semantic: {
            css: string | undefined;
            tokens: any[] | undefined;
        };
        global: {
            css: string | undefined;
            tokens: any[] | undefined;
        };
        style: string | undefined;
    };
    getComponent(name: string | undefined, params: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    getDirective(name: string | undefined, params: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    getCustomPreset(name: string | undefined, preset: any, selector: string, params: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    getLayerOrderCSS(name?: string): string;
    transformCSS(name: string | undefined, css: string, type?: string, mode?: string): string;
    getCommonStyleSheet(name: string | undefined, params: any, props?: {}): any;
    getStyleSheet(name: string, params: any, props?: {}): string;
    onStyleMounted(name: string): void;
    onStyleUpdated(name: string): void;
    onStyleLoaded(event: any, { name }: {
        name: any;
    }): void;
};

declare const _default$4: (color1: string, color2: string, weight: number) => string;

declare const _default$3: (color: string) => string | ColorScale;

declare const _default$2: (color: string, percent: number) => string;

declare const _default$1: (color: string, percent: number) => string;

declare function css(strings: TemplateStringsArray | StyleType, ...exprs: unknown[]): string | undefined;

declare const $dt: (tokenPath: string) => {
    name: string;
    variable: string;
    value: unknown;
};
declare const dt: (...args: Parameters<typeof dtwt> extends [unknown, ...infer Rest] ? Rest : never) => any;
declare const dtwt: (theme: any | undefined, tokenPath: string, fallback?: string, type?: string) => any;

declare const $t: (theme?: any) => {
    preset(value: any): /*elided*/ any;
    options(value: any): /*elided*/ any;
    primaryPalette(primary: any): /*elided*/ any;
    surfacePalette(surface: any): /*elided*/ any;
    define({ useDefaultPreset, useDefaultOptions }?: {
        useDefaultPreset?: boolean | undefined;
        useDefaultOptions?: boolean | undefined;
    }): {
        preset: any;
        options: any;
    };
    update({ mergePresets, mergeOptions }?: {
        mergePresets?: boolean | undefined;
        mergeOptions?: boolean | undefined;
    }): {
        preset: any;
        options: any;
    };
    use(options: any): {
        preset: any;
        options: any;
    };
};

interface toVariableOptions {
    prefix?: string;
    selector?: string;
    excludedKeyRegex?: RegExp;
}
interface toVariableOutput {
    value: string[];
    tokens: string[];
    declarations: string;
    css: string;
}
declare function export_default(theme: any, options?: toVariableOptions): toVariableOutput;

declare const ThemeService: _primeuix_utils_eventbus.EventBusOptions;

interface StyleSheetProps {
    attrs?: Record<string, unknown>;
}
interface StyleMeta<E = HTMLStyleElement> {
    name?: string;
    css?: string;
    attrs?: Record<string, unknown>;
    markup?: string;
    element?: E;
}
declare class StyleSheet<E = HTMLStyleElement> {
    _styles: Map<string, StyleMeta<E>>;
    _attrs: Record<string, unknown>;
    constructor({ attrs }?: StyleSheetProps);
    get(key: string): StyleMeta<E> | undefined;
    has(key: string): boolean;
    delete(key: string): void;
    clear(): void;
    add(key: string, css?: string): void;
    update(): void;
    getStyles(): Map<string, StyleMeta<E>>;
    getAllCSS(): (string | undefined)[];
    getAllMarkup(): (string | undefined)[];
    getAllElements(): (E | undefined)[];
    /**
     * Used to create a style element.
     *
     * @param {StyleMeta} meta
     * @returns {HTMLStyleElement | undefined}
     */
    createStyleElement(meta?: StyleMeta): E | undefined;
}

declare const EXPR_REGEX: RegExp;
declare const CALC_REGEX: RegExp;
declare const VAR_REGEX: RegExp;
declare function toTokenKey(str: string): string;
declare function merge(value1: any, value2: any): void;
declare function toValue(value: any): any;
declare function toUnit(value: string, variable?: string): string;
declare function toNormalizePrefix(prefix: string): string;
declare function toNormalizeVariable(prefix?: string, variable?: string): string;
declare function getVariableName(prefix?: string, variable?: string): string;
declare function hasOddBraces(str?: string): boolean;
declare function getVariableValue(value: any, variable?: string, prefix?: string, excludedKeyRegexes?: RegExp[], fallback?: string): string | undefined;
declare function getComputedValue(obj: {} | undefined, value: any): any;
declare function setProperty(properties: string[], key: string, value?: string): void;
declare function getRule(selector: string, properties: string): string;
declare function evaluateDtExpressions(input: string, fn: (...args: any[]) => string): string;

declare const _default: {
    regex: {
        rules: {
            class: {
                pattern: RegExp;
                resolve(value: string): {
                    type: string;
                    selector: string;
                    matched: boolean;
                };
            };
            attr: {
                pattern: RegExp;
                resolve(value: string): {
                    type: string;
                    selector: string;
                    matched: boolean;
                };
            };
            media: {
                pattern: RegExp;
                resolve(value: string): {
                    type: string;
                    selector: string;
                    matched: boolean;
                };
            };
            system: {
                pattern: RegExp;
                resolve(value: string): {
                    type: string;
                    selector: string;
                    matched: boolean;
                };
            };
            custom: {
                resolve(value: string): {
                    type: string;
                    selector: string;
                    matched: boolean;
                };
            };
        };
        resolve(value: any): any[];
    };
    _toVariables(theme: any, options: any): toVariableOutput;
    getCommon({ name, theme, params, set, defaults }: any): {
        primitive: {
            css: string | undefined;
            tokens: any;
        };
        semantic: {
            css: string | undefined;
            tokens: any[] | undefined;
        };
        global: {
            css: string | undefined;
            tokens: any[] | undefined;
        };
        style: string | undefined;
    };
    getPreset({ name, preset, options, params, set, defaults, selector }: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    getPresetC({ name, theme, params, set, defaults }: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    getPresetD({ name, theme, params, set, defaults }: any): {
        css: string | undefined;
        tokens: any[] | undefined;
        style: string | undefined;
    };
    applyDarkColorScheme(options: any): boolean;
    getColorSchemeOption(options: any, defaults: any): any[];
    getLayerOrder(name: string, options: any | undefined, params: any, defaults: any): string;
    getCommonStyleSheet({ name, theme, params, props, set, defaults }: any): any;
    getStyleSheet({ name, theme, params, props, set, defaults }: any): string;
    createTokens(obj: any | undefined, defaults: any, parentKey?: string, parentPath?: string, tokens?: any): any;
    getTokenValue(tokens: any, path: string, defaults: any): any;
    getSelectorRule(selector1: any, selector2: any, type: string, css: string): string;
    transformCSS(name: string, css: string, mode?: string, type?: string, options?: any, set?: any, defaults?: any, selector?: string): string;
};

interface ThemeOptions {
    /**
     * The prefix for the theme
     * @default 'p'
     */
    prefix?: string;
    /**
     * Dark mode selector
     * @default 'system'
     */
    darkModeSelector?: string;
    /**
     * Whether to use the css layer
     * @default false
     */
    cssLayer?: boolean | {
        name?: string;
        order?: string;
    };
}
interface StyleOptions {
    dt: (key: string, fallback?: string | number | Pick<StyleOptions, 'dt'>) => string | number | undefined;
}
declare type StyleType<T = StyleOptions> = string | ((options?: T) => string);
type ColorScale = {
    0?: string;
    50?: string;
    100?: string;
    200?: string;
    300?: string;
    400?: string;
    500?: string;
    600?: string;
    700?: string;
    800?: string;
    900?: string;
    950?: string;
};

export { $dt, $t, CALC_REGEX, type ColorScale, EXPR_REGEX, type StyleMeta, type StyleOptions, StyleSheet, type StyleSheetProps, type StyleType, _default$5 as Theme, type ThemeOptions, ThemeService, _default as ThemeUtils, VAR_REGEX, css, definePreset, dt, dtwt, evaluateDtExpressions, getComputedValue, getRule, getVariableName, getVariableValue, hasOddBraces, merge, _default$4 as mix, _default$3 as palette, setProperty, _default$2 as shade, _default$1 as tint, toNormalizePrefix, toNormalizeVariable, toTokenKey, toUnit, toValue, export_default as toVariables, updatePreset, updatePrimaryPalette, updateSurfacePalette, usePreset, useTheme };
