{"version": 3, "sources": ["../../src/inplace/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-inplace-display {\n        display: inline-block;\n        cursor: pointer;\n        border: 1px solid transparent;\n        padding: dt('inplace.padding');\n        border-radius: dt('inplace.border.radius');\n        transition:\n            background dt('inplace.transition.duration'),\n            color dt('inplace.transition.duration'),\n            outline-color dt('inplace.transition.duration'),\n            box-shadow dt('inplace.transition.duration');\n        outline-color: transparent;\n    }\n\n    .p-inplace-display:not(.p-disabled):hover {\n        background: dt('inplace.display.hover.background');\n        color: dt('inplace.display.hover.color');\n    }\n\n    .p-inplace-display:focus-visible {\n        box-shadow: dt('inplace.focus.ring.shadow');\n        outline: dt('inplace.focus.ring.width') dt('inplace.focus.ring.style') dt('inplace.focus.ring.color');\n        outline-offset: dt('inplace.focus.ring.offset');\n    }\n\n    .p-inplace-content {\n        display: block;\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}