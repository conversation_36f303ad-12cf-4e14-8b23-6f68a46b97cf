{"version": 3, "sources": ["../../src/blockui/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-blockui {\n        position: relative;\n    }\n\n    .p-blockui-mask {\n        border-radius: dt('blockui.border.radius');\n    }\n\n    .p-blockui-mask.p-overlay-mask {\n        position: absolute;\n    }\n\n    .p-blockui-mask-document.p-overlay-mask {\n        position: fixed;\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}