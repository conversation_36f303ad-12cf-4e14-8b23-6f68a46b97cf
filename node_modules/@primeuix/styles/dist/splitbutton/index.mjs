var style="\n    .p-splitbutton {\n        display: inline-flex;\n        position: relative;\n        border-radius: dt('splitbutton.border.radius');\n    }\n\n    .p-splitbutton-button.p-button {\n        border-start-end-radius: 0;\n        border-end-end-radius: 0;\n        border-inline-end: 0 none;\n    }\n\n    .p-splitbutton-button.p-button:focus-visible,\n    .p-splitbutton-dropdown.p-button:focus-visible {\n        z-index: 1;\n    }\n\n    .p-splitbutton-button.p-button:not(:disabled):hover,\n    .p-splitbutton-button.p-button:not(:disabled):active {\n        border-inline-end: 0 none;\n    }\n\n    .p-splitbutton-dropdown.p-button {\n        border-start-start-radius: 0;\n        border-end-start-radius: 0;\n    }\n\n    .p-splitbutton .p-menu {\n        min-width: 100%;\n    }\n\n    .p-splitbutton-fluid {\n        display: flex;\n    }\n\n    .p-splitbutton-rounded .p-splitbutton-dropdown.p-button {\n        border-start-end-radius: dt('splitbutton.rounded.border.radius');\n        border-end-end-radius: dt('splitbutton.rounded.border.radius');\n    }\n\n    .p-splitbutton-rounded .p-splitbutton-button.p-button {\n        border-start-start-radius: dt('splitbutton.rounded.border.radius');\n        border-end-start-radius: dt('splitbutton.rounded.border.radius');\n    }\n\n    .p-splitbutton-raised {\n        box-shadow: dt('splitbutton.raised.shadow');\n    }\n";export{style};//# sourceMappingURL=index.mjs.map