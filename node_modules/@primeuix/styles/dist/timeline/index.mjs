var style="\n    .p-timeline {\n        display: flex;\n        flex-grow: 1;\n        flex-direction: column;\n        direction: ltr;\n    }\n\n    .p-timeline-left .p-timeline-event-opposite {\n        text-align: right;\n    }\n\n    .p-timeline-left .p-timeline-event-content {\n        text-align: left;\n    }\n\n    .p-timeline-right .p-timeline-event {\n        flex-direction: row-reverse;\n    }\n\n    .p-timeline-right .p-timeline-event-opposite {\n        text-align: left;\n    }\n\n    .p-timeline-right .p-timeline-event-content {\n        text-align: right;\n    }\n\n    .p-timeline-vertical.p-timeline-alternate .p-timeline-event:nth-child(even) {\n        flex-direction: row-reverse;\n    }\n\n    .p-timeline-vertical.p-timeline-alternate .p-timeline-event:nth-child(odd) .p-timeline-event-opposite {\n        text-align: right;\n    }\n\n    .p-timeline-vertical.p-timeline-alternate .p-timeline-event:nth-child(odd) .p-timeline-event-content {\n        text-align: left;\n    }\n\n    .p-timeline-vertical.p-timeline-alternate .p-timeline-event:nth-child(even) .p-timeline-event-opposite {\n        text-align: left;\n    }\n\n    .p-timeline-vertical.p-timeline-alternate .p-timeline-event:nth-child(even) .p-timeline-event-content {\n        text-align: right;\n    }\n\n    .p-timeline-vertical .p-timeline-event-opposite,\n    .p-timeline-vertical .p-timeline-event-content {\n        padding: dt('timeline.vertical.event.content.padding');\n    }\n\n    .p-timeline-vertical .p-timeline-event-connector {\n        width: dt('timeline.event.connector.size');\n    }\n\n    .p-timeline-event {\n        display: flex;\n        position: relative;\n        min-height: dt('timeline.event.min.height');\n    }\n\n    .p-timeline-event:last-child {\n        min-height: 0;\n    }\n\n    .p-timeline-event-opposite {\n        flex: 1;\n    }\n\n    .p-timeline-event-content {\n        flex: 1;\n    }\n\n    .p-timeline-event-separator {\n        flex: 0;\n        display: flex;\n        align-items: center;\n        flex-direction: column;\n    }\n\n    .p-timeline-event-marker {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n        align-self: baseline;\n        border-width: dt('timeline.event.marker.border.width');\n        border-style: solid;\n        border-color: dt('timeline.event.marker.border.color');\n        border-radius: dt('timeline.event.marker.border.radius');\n        width: dt('timeline.event.marker.size');\n        height: dt('timeline.event.marker.size');\n        background: dt('timeline.event.marker.background');\n    }\n\n    .p-timeline-event-marker::before {\n        content: ' ';\n        border-radius: dt('timeline.event.marker.content.border.radius');\n        width: dt('timeline.event.marker.content.size');\n        height: dt('timeline.event.marker.content.size');\n        background: dt('timeline.event.marker.content.background');\n    }\n\n    .p-timeline-event-marker::after {\n        content: ' ';\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        border-radius: dt('timeline.event.marker.border.radius');\n        box-shadow: dt('timeline.event.marker.content.inset.shadow');\n    }\n\n    .p-timeline-event-connector {\n        flex-grow: 1;\n        background: dt('timeline.event.connector.color');\n    }\n\n    .p-timeline-horizontal {\n        flex-direction: row;\n    }\n\n    .p-timeline-horizontal .p-timeline-event {\n        flex-direction: column;\n        flex: 1;\n    }\n\n    .p-timeline-horizontal .p-timeline-event:last-child {\n        flex: 0;\n    }\n\n    .p-timeline-horizontal .p-timeline-event-separator {\n        flex-direction: row;\n    }\n\n    .p-timeline-horizontal .p-timeline-event-connector {\n        width: 100%;\n        height: dt('timeline.event.connector.size');\n    }\n\n    .p-timeline-horizontal .p-timeline-event-opposite,\n    .p-timeline-horizontal .p-timeline-event-content {\n        padding: dt('timeline.horizontal.event.content.padding');\n    }\n\n    .p-timeline-horizontal.p-timeline-alternate .p-timeline-event:nth-child(even) {\n        flex-direction: column-reverse;\n    }\n\n    .p-timeline-bottom .p-timeline-event {\n        flex-direction: column-reverse;\n    }\n";export{style};//# sourceMappingURL=index.mjs.map