{"version": 3, "sources": ["../../src/inputchips/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-inputchips {\n        display: inline-flex;\n    }\n\n    .p-inputchips-input {\n        margin: 0;\n        list-style-type: none;\n        cursor: text;\n        overflow: hidden;\n        display: flex;\n        align-items: center;\n        flex-wrap: wrap;\n        padding: calc(dt('inputchips.padding.y') / 2) dt('inputchips.padding.x');\n        gap: calc(dt('inputchips.padding.y') / 2);\n        color: dt('inputchips.color');\n        background: dt('inputchips.background');\n        border: 1px solid dt('inputchips.border.color');\n        border-radius: dt('inputchips.border.radius');\n        width: 100%;\n        transition:\n            background dt('inputchips.transition.duration'),\n            color dt('inputchips.transition.duration'),\n            border-color dt('inputchips.transition.duration'),\n            outline-color dt('inputchips.transition.duration'),\n            box-shadow dt('inputchips.transition.duration');\n        outline-color: transparent;\n        box-shadow: dt('inputchips.shadow');\n    }\n\n    .p-inputchips:not(.p-disabled):hover .p-inputchips-input {\n        border-color: dt('inputchips.hover.border.color');\n    }\n\n    .p-inputchips:not(.p-disabled).p-focus .p-inputchips-input {\n        border-color: dt('inputchips.focus.border.color');\n        box-shadow: dt('inputchips.focus.ring.shadow');\n        outline: dt('inputchips.focus.ring.width') dt('inputchips.focus.ring.style') dt('inputchips.focus.ring.color');\n        outline-offset: dt('inputchips.focus.ring.offset');\n    }\n\n    .p-inputchips.p-invalid .p-inputchips-input {\n        border-color: dt('inputchips.invalid.border.color');\n    }\n\n    .p-variant-filled.p-inputchips-input {\n        background: dt('inputchips.filled.background');\n    }\n\n    .p-inputchips:not(.p-disabled).p-focus .p-variant-filled.p-inputchips-input {\n        background: dt('inputchips.filled.focus.background');\n    }\n\n    .p-inputchips.p-disabled .p-inputchips-input {\n        opacity: 1;\n        background: dt('inputchips.disabled.background');\n        color: dt('inputchips.disabled.color');\n    }\n\n    .p-inputchips-chip.p-chip {\n        padding-top: calc(dt('inputchips.padding.y') / 2);\n        padding-bottom: calc(dt('inputchips.padding.y') / 2);\n        border-radius: dt('inputchips.chip.border.radius');\n        transition:\n            background dt('inputchips.transition.duration'),\n            color dt('inputchips.transition.duration');\n    }\n\n    .p-inputchips-chip-item.p-focus .p-inputchips-chip {\n        background: dt('inputchips.chip.focus.background');\n        color: dt('inputchips.chip.focus.color');\n    }\n\n    .p-inputchips-input:has(.p-inputchips-chip) {\n        padding-left: calc(dt('inputchips.padding.y') / 2);\n        padding-right: calc(dt('inputchips.padding.y') / 2);\n    }\n\n    .p-inputchips-input-item {\n        flex: 1 1 auto;\n        display: inline-flex;\n        padding-top: calc(dt('inputchips.padding.y') / 2);\n        padding-bottom: calc(dt('inputchips.padding.y') / 2);\n    }\n\n    .p-inputchips-input-item input {\n        border: 0 none;\n        outline: 0 none;\n        background: transparent;\n        margin: 0;\n        padding: 0;\n        box-shadow: none;\n        border-radius: 0;\n        width: 100%;\n        font-family: inherit;\n        font-feature-settings: inherit;\n        font-size: 1rem;\n        color: inherit;\n    }\n\n    .p-inputchips-input-item input::placeholder {\n        color: dt('inputchips.placeholder.color');\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}