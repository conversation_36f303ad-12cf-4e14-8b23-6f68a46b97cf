{"version": 3, "sources": ["../../src/overlaybadge/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-overlaybadge {\n        position: relative;\n    }\n\n    .p-overlaybadge .p-badge {\n        position: absolute;\n        inset-block-start: 0;\n        inset-inline-end: 0;\n        transform: translate(50%, -50%);\n        transform-origin: 100% 0;\n        margin: 0;\n        outline-width: dt('overlaybadge.outline.width');\n        outline-style: solid;\n        outline-color: dt('overlaybadge.outline.color');\n    }\n\n    .p-overlaybadge .p-badge:dir(rtl) {\n        transform: translate(-50%, -50%);\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}