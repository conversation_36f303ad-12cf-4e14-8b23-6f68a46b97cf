{"version": 3, "sources": ["../../src/treetable/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-treetable {\n        position: relative;\n    }\n\n    .p-treetable-table {\n        border-spacing: 0;\n        border-collapse: separate;\n        width: 100%;\n    }\n\n    .p-treetable-scrollable > .p-treetable-table-container {\n        position: relative;\n    }\n\n    .p-treetable-scrollable-table > .p-treetable-thead {\n        inset-block-start: 0;\n        z-index: 1;\n    }\n\n    .p-treetable-scrollable-table > .p-treetable-frozen-tbody {\n        position: sticky;\n        z-index: 1;\n    }\n\n    .p-treetable-scrollable-table > .p-treetable-tfoot {\n        inset-block-end: 0;\n        z-index: 1;\n    }\n\n    .p-treetable-scrollable .p-treetable-frozen-column {\n        position: sticky;\n        background: dt('treetable.header.cell.background');\n    }\n\n    .p-treetable-scrollable th.p-treetable-frozen-column {\n        z-index: 1;\n    }\n\n    .p-treetable-scrollable > .p-treetable-table-container > .p-treetable-table > .p-treetable-thead {\n        background: dt('treetable.header.cell.background');\n    }\n\n    .p-treetable-scrollable > .p-treetable-table-container > .p-treetable-table > .p-treetable-tfoot {\n        background: dt('treetable.footer.cell.background');\n    }\n\n    .p-treetable-flex-scrollable {\n        display: flex;\n        flex-direction: column;\n        height: 100%;\n    }\n\n    .p-treetable-flex-scrollable > .p-treetable-table-container {\n        display: flex;\n        flex-direction: column;\n        flex: 1;\n        height: 100%;\n    }\n\n    .p-treetable-scrollable-table > .p-treetable-tbody > .p-treetable-row-group-header {\n        position: sticky;\n        z-index: 1;\n    }\n\n    .p-treetable-resizable-table > .p-treetable-thead > tr > th,\n    .p-treetable-resizable-table > .p-treetable-tfoot > tr > td,\n    .p-treetable-resizable-table > .p-treetable-tbody > tr > td {\n        overflow: hidden;\n        white-space: nowrap;\n    }\n\n    .p-treetable-resizable-table > .p-treetable-thead > tr > th.p-treetable-resizable-column:not(.p-treetable-frozen-column) {\n        background-clip: padding-box;\n        position: relative;\n    }\n\n    .p-treetable-resizable-table-fit > .p-treetable-thead > tr > th.p-treetable-resizable-column:last-child .p-treetable-column-resizer {\n        display: none;\n    }\n\n    .p-treetable-column-resizer {\n        display: block;\n        position: absolute;\n        inset-block-start: 0;\n        inset-inline-end: 0;\n        margin: 0;\n        width: dt('treetable.column.resizer.width');\n        height: 100%;\n        padding: 0;\n        cursor: col-resize;\n        border: 1px solid transparent;\n    }\n\n    .p-treetable-column-header-content {\n        display: flex;\n        align-items: center;\n        gap: dt('treetable.header.cell.gap');\n    }\n\n    .p-treetable-column-resize-indicator {\n        width: dt('treetable.resize.indicator.width');\n        position: absolute;\n        z-index: 10;\n        display: none;\n        background: dt('treetable.resize.indicator.color');\n    }\n\n    .p-treetable-mask {\n        position: absolute;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 2;\n    }\n\n    .p-treetable-paginator-top {\n        border-color: dt('treetable.paginator.top.border.color');\n        border-style: solid;\n        border-width: dt('treetable.paginator.top.border.width');\n    }\n\n    .p-treetable-paginator-bottom {\n        border-color: dt('treetable.paginator.bottom.border.color');\n        border-style: solid;\n        border-width: dt('treetable.paginator.bottom.border.width');\n    }\n\n    .p-treetable-header {\n        background: dt('treetable.header.background');\n        color: dt('treetable.header.color');\n        border-color: dt('treetable.header.border.color');\n        border-style: solid;\n        border-width: dt('treetable.header.border.width');\n        padding: dt('treetable.header.padding');\n    }\n\n    .p-treetable-footer {\n        background: dt('treetable.footer.background');\n        color: dt('treetable.footer.color');\n        border-color: dt('treetable.footer.border.color');\n        border-style: solid;\n        border-width: dt('treetable.footer.border.width');\n        padding: dt('treetable.footer.padding');\n    }\n\n    .p-treetable-header-cell {\n        padding: dt('treetable.header.cell.padding');\n        background: dt('treetable.header.cell.background');\n        border-color: dt('treetable.header.cell.border.color');\n        border-style: solid;\n        border-width: 0 0 1px 0;\n        color: dt('treetable.header.cell.color');\n        font-weight: normal;\n        text-align: start;\n        transition:\n            background dt('treetable.transition.duration'),\n            color dt('treetable.transition.duration'),\n            border-color dt('treetable.transition.duration'),\n            outline-color dt('treetable.transition.duration'),\n            box-shadow dt('treetable.transition.duration');\n    }\n\n    .p-treetable-column-title {\n        font-weight: dt('treetable.column.title.font.weight');\n    }\n\n    .p-treetable-tbody > tr {\n        outline-color: transparent;\n        background: dt('treetable.row.background');\n        color: dt('treetable.row.color');\n        transition:\n            background dt('treetable.transition.duration'),\n            color dt('treetable.transition.duration'),\n            border-color dt('treetable.transition.duration'),\n            outline-color dt('treetable.transition.duration'),\n            box-shadow dt('treetable.transition.duration');\n    }\n\n    .p-treetable-tbody > tr > td {\n        text-align: start;\n        border-color: dt('treetable.body.cell.border.color');\n        border-style: solid;\n        border-width: 0 0 1px 0;\n        padding: dt('treetable.body.cell.padding');\n    }\n\n    .p-treetable-hoverable .p-treetable-tbody > tr:not(.p-treetable-row-selected):hover {\n        background: dt('treetable.row.hover.background');\n        color: dt('treetable.row.hover.color');\n    }\n\n    .p-treetable-tbody > tr.p-treetable-row-selected {\n        background: dt('treetable.row.selected.background');\n        color: dt('treetable.row.selected.color');\n    }\n\n    .p-treetable-tbody > tr:has(+ .p-treetable-row-selected) > td {\n        border-block-end-color: dt('treetable.body.cell.selected.border.color');\n    }\n\n    .p-treetable-tbody > tr.p-treetable-row-selected > td {\n        border-block-end-color: dt('treetable.body.cell.selected.border.color');\n    }\n\n    .p-treetable-tbody > tr:focus-visible,\n    .p-treetable-tbody > tr.p-treetable-contextmenu-row-selected {\n        box-shadow: dt('treetable.row.focus.ring.shadow');\n        outline: dt('treetable.row.focus.ring.width') dt('treetable.row.focus.ring.style') dt('treetable.row.focus.ring.color');\n        outline-offset: dt('treetable.row.focus.ring.offset');\n    }\n\n    .p-treetable-tfoot > tr > td {\n        text-align: start;\n        padding: dt('treetable.footer.cell.padding');\n        border-color: dt('treetable.footer.cell.border.color');\n        border-style: solid;\n        border-width: 0 0 1px 0;\n        color: dt('treetable.footer.cell.color');\n        background: dt('treetable.footer.cell.background');\n    }\n\n    .p-treetable-column-footer {\n        font-weight: dt('treetable.column.footer.font.weight');\n    }\n\n    .p-treetable-sortable-column {\n        cursor: pointer;\n        user-select: none;\n        outline-color: transparent;\n    }\n\n    .p-treetable-column-title,\n    .p-treetable-sort-icon,\n    .p-treetable-sort-badge {\n        vertical-align: middle;\n    }\n\n    .p-treetable-sort-icon {\n        color: dt('treetable.sort.icon.color');\n        font-size: dt('treetable.sort.icon.size');\n        width: dt('treetable.sort.icon.size');\n        height: dt('treetable.sort.icon.size');\n        transition: color dt('treetable.transition.duration');\n    }\n\n    .p-treetable-sortable-column:not(.p-treetable-column-sorted):hover {\n        background: dt('treetable.header.cell.hover.background');\n        color: dt('treetable.header.cell.hover.color');\n    }\n\n    .p-treetable-sortable-column:not(.p-treetable-column-sorted):hover .p-treetable-sort-icon {\n        color: dt('treetable.sort.icon.hover.color');\n    }\n\n    .p-treetable-column-sorted {\n        background: dt('treetable.header.cell.selected.background');\n        color: dt('treetable.header.cell.selected.color');\n    }\n\n    .p-treetable-column-sorted .p-treetable-sort-icon {\n        color: dt('treetable.header.cell.selected.color');\n    }\n\n    .p-treetable-sortable-column:focus-visible {\n        box-shadow: dt('treetable.header.cell.focus.ring.shadow');\n        outline: dt('treetable.header.cell.focus.ring.width') dt('treetable.header.cell.focus.ring.style') dt('treetable.header.cell.focus.ring.color');\n        outline-offset: dt('treetable.header.cell.focus.ring.offset');\n    }\n\n    .p-treetable-hoverable .p-treetable-selectable-row {\n        cursor: pointer;\n    }\n\n    .p-treetable-loading-icon {\n        font-size: dt('treetable.loading.icon.size');\n        width: dt('treetable.loading.icon.size');\n        height: dt('treetable.loading.icon.size');\n    }\n\n    .p-treetable-gridlines .p-treetable-header {\n        border-width: 1px 1px 0 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-footer {\n        border-width: 0 1px 1px 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-paginator-top {\n        border-width: 1px 1px 0 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-paginator-bottom {\n        border-width: 0 1px 1px 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-thead > tr > th {\n        border-width: 1px 0 1px 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-thead > tr > th:last-child {\n        border-width: 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tbody > tr > td {\n        border-width: 1px 0 0 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tbody > tr > td:last-child {\n        border-width: 1px 1px 0 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tbody > tr:last-child > td {\n        border-width: 1px 0 1px 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tbody > tr:last-child > td:last-child {\n        border-width: 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tfoot > tr > td {\n        border-width: 1px 0 1px 1px;\n    }\n\n    .p-treetable-gridlines .p-treetable-tfoot > tr > td:last-child {\n        border-width: 1px 1px 1px 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines .p-treetable-thead + .p-treetable-tfoot > tr > td {\n        border-width: 0 0 1px 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines .p-treetable-thead + .p-treetable-tfoot > tr > td:last-child {\n        border-width: 0 1px 1px 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines:has(.p-treetable-thead):has(.p-treetable-tbody) .p-treetable-tbody > tr > td {\n        border-width: 0 0 1px 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines:has(.p-treetable-thead):has(.p-treetable-tbody) .p-treetable-tbody > tr > td:last-child {\n        border-width: 0 1px 1px 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines:has(.p-treetable-tbody):has(.p-treetable-tfoot) .p-treetable-tbody > tr:last-child > td {\n        border-width: 0 0 0 1px;\n    }\n\n    .p-treetable.p-treetable-gridlines:has(.p-treetable-tbody):has(.p-treetable-tfoot) .p-treetable-tbody > tr:last-child > td:last-child {\n        border-width: 0 1px 0 1px;\n    }\n\n    .p-treetable.p-treetable-sm .p-treetable-header {\n        padding: 0.375rem 0.5rem;\n    }\n\n    .p-treetable.p-treetable-sm .p-treetable-thead > tr > th {\n        padding: 0.375rem 0.5rem;\n    }\n\n    .p-treetable.p-treetable-sm .p-treetable-tbody > tr > td {\n        padding: 0.375rem 0.5rem;\n    }\n\n    .p-treetable.p-treetable-sm .p-treetable-tfoot > tr > td {\n        padding: 0.375rem 0.5rem;\n    }\n\n    .p-treetable.p-treetable-sm .p-treetable-footer {\n        padding: 0.375rem 0.5rem;\n    }\n\n    .p-treetable.p-treetable-lg .p-treetable-header {\n        padding: 0.9375rem 1.25rem;\n    }\n\n    .p-treetable.p-treetable-lg .p-treetable-thead > tr > th {\n        padding: 0.9375rem 1.25rem;\n    }\n\n    .p-treetable.p-treetable-lg .p-treetable-tbody > tr > td {\n        padding: 0.9375rem 1.25rem;\n    }\n\n    .p-treetable.p-treetable-lg .p-treetable-tfoot > tr > td {\n        padding: 0.9375rem 1.25rem;\n    }\n\n    .p-treetable.p-treetable-lg .p-treetable-footer {\n        padding: 0.9375rem 1.25rem;\n    }\n\n    .p-treetable-body-cell-content {\n        display: flex;\n        align-items: center;\n        gap: dt('treetable.body.cell.gap');\n    }\n\n    .p-treetable-tbody > tr.p-treetable-row-selected .p-treetable-node-toggle-button {\n        color: inherit;\n    }\n\n    .p-treetable-node-toggle-button {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        overflow: hidden;\n        position: relative;\n        width: dt('treetable.node.toggle.button.size');\n        height: dt('treetable.node.toggle.button.size');\n        color: dt('treetable.node.toggle.button.color');\n        border: 0 none;\n        background: transparent;\n        cursor: pointer;\n        border-radius: dt('treetable.node.toggle.button.border.radius');\n        transition:\n            background dt('treetable.transition.duration'),\n            color dt('treetable.transition.duration'),\n            border-color dt('treetable.transition.duration'),\n            outline-color dt('treetable.transition.duration'),\n            box-shadow dt('treetable.transition.duration');\n        outline-color: transparent;\n        user-select: none;\n    }\n\n    .p-treetable-node-toggle-button:enabled:hover {\n        color: dt('treetable.node.toggle.button.hover.color');\n        background: dt('treetable.node.toggle.button.hover.background');\n    }\n\n    .p-treetable-tbody > tr.p-treetable-row-selected .p-treetable-node-toggle-button:hover {\n        background: dt('treetable.node.toggle.button.selected.hover.background');\n        color: dt('treetable.node.toggle.button.selected.hover.color');\n    }\n\n    .p-treetable-node-toggle-button:focus-visible {\n        box-shadow: dt('treetable.node.toggle.button.focus.ring.shadow');\n        outline: dt('treetable.node.toggle.button.focus.ring.width') dt('treetable.node.toggle.button.focus.ring.style') dt('treetable.node.toggle.button.focus.ring.color');\n        outline-offset: dt('treetable.node.toggle.button.focus.ring.offset');\n    }\n\n    .p-treetable-node-toggle-icon:dir(rtl) {\n        transform: rotate(180deg);\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}