{"version": 3, "sources": ["../../src/scrolltop/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-scrolltop.p-button {\n        position: fixed !important;\n        inset-block-end: 20px;\n        inset-inline-end: 20px;\n    }\n\n    .p-scrolltop-sticky.p-button {\n        position: sticky !important;\n        display: flex;\n        margin-inline-start: auto;\n    }\n\n    .p-scrolltop-enter-from {\n        opacity: 0;\n    }\n\n    .p-scrolltop-enter-active {\n        transition: opacity 0.15s;\n    }\n\n    .p-scrolltop.p-scrolltop-leave-to {\n        opacity: 0;\n    }\n\n    .p-scrolltop-leave-active {\n        transition: opacity 0.15s;\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}