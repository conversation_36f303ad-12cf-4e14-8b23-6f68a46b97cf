var style="\n    .p-tree {\n        display: block;\n        background: dt('tree.background');\n        color: dt('tree.color');\n        padding: dt('tree.padding');\n    }\n\n    .p-tree-root-children,\n    .p-tree-node-children {\n        display: flex;\n        list-style-type: none;\n        flex-direction: column;\n        margin: 0;\n        gap: dt('tree.gap');\n    }\n\n    .p-tree-root-children {\n        padding: 0;\n        padding-block-start: dt('tree.gap');\n    }\n\n    .p-tree-node-children {\n        padding: 0;\n        padding-block-start: dt('tree.gap');\n        padding-inline-start: dt('tree.indent');\n    }\n\n    .p-tree-node {\n        padding: 0;\n        outline: 0 none;\n    }\n\n    .p-tree-node-content {\n        border-radius: dt('tree.node.border.radius');\n        padding: dt('tree.node.padding');\n        display: flex;\n        align-items: center;\n        outline-color: transparent;\n        color: dt('tree.node.color');\n        gap: dt('tree.node.gap');\n        transition:\n            background dt('tree.transition.duration'),\n            color dt('tree.transition.duration'),\n            outline-color dt('tree.transition.duration'),\n            box-shadow dt('tree.transition.duration');\n    }\n\n    .p-tree-node-content[data-p-dragging] {\n        outline: 1px dashed dt('primary.color');\n        outline-offset: -1px;\n    }\n\n    .p-tree-node-content[data-pc-section=\"drag-image\"] {\n        background: dt('tree.background');\n    }\n\n    .p-tree-node:focus-visible > .p-tree-node-content {\n        box-shadow: dt('tree.node.focus.ring.shadow');\n        outline: dt('tree.node.focus.ring.width') dt('tree.node.focus.ring.style') dt('tree.node.focus.ring.color');\n        outline-offset: dt('tree.node.focus.ring.offset');\n    }\n\n    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover {\n        background: dt('tree.node.hover.background');\n        color: dt('tree.node.hover.color');\n    }\n\n    .p-tree-node-content.p-tree-node-selectable:not(.p-tree-node-selected):hover .p-tree-node-icon {\n        color: dt('tree.node.icon.hover.color');\n    }\n\n    .p-tree-node-content.p-tree-node-selected {\n        background: dt('tree.node.selected.background');\n        color: dt('tree.node.selected.color');\n    }\n\n    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button {\n        color: inherit;\n    }\n\n    .p-tree-node-content.p-tree-node-dragover {\n        background: dt('tree.node.hover.background');\n        color: dt('tree.node.hover.color');\n    }\n\n    .p-tree-node-drop-point {\n\t\toutline: 1px solid dt('primary.color');\n\t}\n\n    .p-tree-node-toggle-button {\n        cursor: pointer;\n        user-select: none;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        overflow: hidden;\n        position: relative;\n        flex-shrink: 0;\n        width: dt('tree.node.toggle.button.size');\n        height: dt('tree.node.toggle.button.size');\n        color: dt('tree.node.toggle.button.color');\n        border: 0 none;\n        background: transparent;\n        border-radius: dt('tree.node.toggle.button.border.radius');\n        transition:\n            background dt('tree.transition.duration'),\n            color dt('tree.transition.duration'),\n            border-color dt('tree.transition.duration'),\n            outline-color dt('tree.transition.duration'),\n            box-shadow dt('tree.transition.duration');\n        outline-color: transparent;\n        padding: 0;\n    }\n\n    .p-tree-node-toggle-button:enabled:hover {\n        background: dt('tree.node.toggle.button.hover.background');\n        color: dt('tree.node.toggle.button.hover.color');\n    }\n\n    .p-tree-node-content.p-tree-node-selected .p-tree-node-toggle-button:hover {\n        background: dt('tree.node.toggle.button.selected.hover.background');\n        color: dt('tree.node.toggle.button.selected.hover.color');\n    }\n\n    .p-tree-root {\n        overflow: auto;\n    }\n\n    .p-tree-node-selectable {\n        cursor: pointer;\n        user-select: none;\n    }\n\n    .p-tree-node-leaf > .p-tree-node-content .p-tree-node-toggle-button {\n        visibility: hidden;\n    }\n\n    .p-tree-node-icon {\n        color: dt('tree.node.icon.color');\n        transition: color dt('tree.transition.duration');\n    }\n\n    .p-tree-node-content.p-tree-node-selected .p-tree-node-icon {\n        color: dt('tree.node.icon.selected.color');\n    }\n\n    .p-tree-filter {\n        margin: dt('tree.filter.margin');\n    }\n\n    .p-tree-filter-input {\n        width: 100%;\n    }\n\n    .p-tree-loading {\n        position: relative;\n        height: 100%;\n    }\n\n    .p-tree-loading-icon {\n        font-size: dt('tree.loading.icon.size');\n        width: dt('tree.loading.icon.size');\n        height: dt('tree.loading.icon.size');\n    }\n\n    .p-tree .p-tree-mask {\n        position: absolute;\n        z-index: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n\n    .p-tree-flex-scrollable {\n        display: flex;\n        flex: 1;\n        height: 100%;\n        flex-direction: column;\n    }\n\n    .p-tree-flex-scrollable .p-tree-root {\n        flex: 1;\n    }\n";export{style};//# sourceMappingURL=index.mjs.map