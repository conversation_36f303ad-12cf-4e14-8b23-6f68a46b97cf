var style="\n    .p-terminal {\n        display: block;\n        height: dt('terminal.height');\n        overflow: auto;\n        background: dt('terminal.background');\n        color: dt('terminal.color');\n        border: 1px solid dt('terminal.border.color');\n        padding: dt('terminal.padding');\n        border-radius: dt('terminal.border.radius');\n    }\n\n    .p-terminal-prompt {\n        display: flex;\n        align-items: center;\n    }\n\n    .p-terminal-prompt-value {\n        flex: 1 1 auto;\n        border: 0 none;\n        background: transparent;\n        color: inherit;\n        padding: 0;\n        outline: 0 none;\n        font-family: inherit;\n        font-feature-settings: inherit;\n        font-size: 1rem;\n    }\n\n    .p-terminal-prompt-label {\n        margin-inline-end: dt('terminal.prompt.gap');\n    }\n\n    .p-terminal-input::-ms-clear {\n        display: none;\n    }\n\n    .p-terminal-command-response {\n        margin: dt('terminal.command.response.margin');\n    }\n";export{style};//# sourceMappingURL=index.mjs.map