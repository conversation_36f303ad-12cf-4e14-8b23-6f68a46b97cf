{"version": 3, "sources": ["../../src/imagecompare/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-imagecompare {\n        position: relative;\n        overflow: hidden;\n        width: 100%;\n        aspect-ratio: 16 / 9;\n    }\n\n    .p-imagecompare img {\n        width: 100%;\n        height: 100%;\n        position: absolute;\n    }\n\n    .p-imagecompare img + img {\n        clip-path: polygon(0 0, dt('imagecompare.scope.x', '50%') 0, dt('imagecompare.scope.x', '50%') 100%, 0 100%);\n    }\n\n    .p-imagecompare:dir(rtl) img + img {\n        clip-path: polygon(calc(100% - dt('imagecompare.scope.x', '50%')) 0, 100% 0, 100% 100%, calc(100% - dt('imagecompare.scope.x', '50%')) 100%);\n    }\n\n    .p-imagecompare-slider {\n        position: relative;\n        -webkit-appearance: none;\n        width: calc(100% + dt('imagecompare.handle.size'));\n        height: 100%;\n        margin-inline-start: calc(-1 * calc(dt('imagecompare.handle.size') / 2));\n        background-color: transparent;\n        outline: none;\n        transition: all dt('imagecompare.handle.transition.duration');\n    }\n\n    .p-imagecompare-slider::-webkit-slider-thumb {\n        -webkit-appearance: none;\n        height: dt('imagecompare.handle.size');\n        width: dt('imagecompare.handle.size');\n        background: dt('imagecompare.handle.background');\n        border: dt('imagecompare.handle.border.width') solid dt('imagecompare.handle.border.color');\n        border-radius: dt('imagecompare.handle.border.radius');\n        background-size: contain;\n        cursor: ew-resize;\n        transition: all dt('imagecompare.handle.transition.duration');\n    }\n\n    .p-imagecompare-slider::-moz-range-thumb {\n        height: dt('imagecompare.handle.size');\n        width: dt('imagecompare.handle.size');\n        background: dt('imagecompare.handle.background');\n        border: dt('imagecompare.handle.border.width') dt('imagecompare.handle.border.style') dt('imagecompare.handle.border.color');\n        border-radius: dt('imagecompare.handle.border.radius');\n        background-size: contain;\n        cursor: ew-resize;\n    }\n\n    .p-imagecompare-slider:focus-visible::-webkit-slider-thumb {\n        box-shadow: dt('imagecompare.handle.focus.ring.shadow');\n        outline: dt('imagecompare.handle.focus.ring.width') dt('imagecompare.handle.focus.ring.style') dt('imagecompare.handle.focus.ring.color');\n        outline-offset: dt('imagecompare.handle.focus.ring.offset');\n    }\n\n    .p-imagecompare-slider:focus-visible::-moz-range-thumb {\n        box-shadow: dt('imagecompare.handle.focus.ring.shadow');\n        outline: dt('imagecompare.handle.focus.ring.width') dt('imagecompare.handle.focus.ring.style') dt('imagecompare.handle.focus.ring.color');\n        outline-offset: dt('imagecompare.handle.focus.ring.offset');\n    }\n\n    .p-imagecompare-slider:hover {\n        width: calc(100% + dt('imagecompare.handle.hover.size'));\n        margin-inline-start: calc(-1 * calc(dt('imagecompare.handle.hover.size') / 2));\n    }\n\n    .p-imagecompare-slider:hover::-webkit-slider-thumb {\n        background: dt('imagecompare.handle.hover.background');\n        border-color: dt('imagecompare.handle.hover.border.color');\n        height: dt('imagecompare.handle.hover.size');\n        width: dt('imagecompare.handle.hover.size');\n    }\n\n    .p-imagecompare-slider:hover::-moz-range-thumb {\n        background: dt('imagecompare.handle.hover.background');\n        border-color: dt('imagecompare.handle.hover.border.color');\n        height: dt('imagecompare.handle.hover.size');\n        width: dt('imagecompare.handle.hover.size');\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}