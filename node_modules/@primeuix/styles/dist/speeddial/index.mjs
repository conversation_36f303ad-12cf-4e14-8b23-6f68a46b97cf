var style="\n    .p-speeddial {\n        position: static;\n        display: flex;\n        gap: dt('speeddial.gap');\n    }\n\n    .p-speeddial-button {\n        z-index: 1;\n    }\n\n    .p-speeddial-button.p-speeddial-rotate {\n        transition:\n            transform 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,\n            background dt('speeddial.transition.duration'),\n            color dt('speeddial.transition.duration'),\n            border-color dt('speeddial.transition.duration'),\n            box-shadow dt('speeddial.transition.duration'),\n            outline-color dt('speeddial.transition.duration');\n        will-change: transform;\n    }\n\n    .p-speeddial-list {\n        margin: 0;\n        padding: 0;\n        list-style: none;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: inset-block-start 0s linear dt('speeddial.transition.duration');\n        pointer-events: none;\n        outline: 0 none;\n        z-index: 2;\n        gap: dt('speeddial.gap');\n    }\n\n    .p-speeddial-item {\n        transform: scale(0);\n        opacity: 0;\n        transition:\n            transform 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,\n            opacity 0.8s;\n        will-change: transform;\n    }\n\n    .p-speeddial-circle .p-speeddial-item,\n    .p-speeddial-semi-circle .p-speeddial-item,\n    .p-speeddial-quarter-circle .p-speeddial-item {\n        position: absolute;\n    }\n\n    .p-speeddial-mask {\n        position: absolute;\n        inset-inline-start: 0;\n        inset-block-start: 0;\n        width: 100%;\n        height: 100%;\n        opacity: 0;\n        background: dt('mask.background');\n        border-radius: 6px;\n        transition: opacity 150ms;\n    }\n\n    .p-speeddial-mask-visible {\n        pointer-events: none;\n        opacity: 1;\n        transition: opacity 150ms;\n    }\n\n    .p-speeddial-open .p-speeddial-list {\n        pointer-events: auto;\n    }\n\n    .p-speeddial-open .p-speeddial-item {\n        transform: scale(1);\n        opacity: 1;\n    }\n\n    .p-speeddial-open .p-speeddial-rotate {\n        transform: rotate(45deg);\n    }\n";export{style};//# sourceMappingURL=index.mjs.map