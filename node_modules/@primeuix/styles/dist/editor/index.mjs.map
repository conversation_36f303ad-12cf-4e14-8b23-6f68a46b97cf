{"version": 3, "sources": ["../../src/editor/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    /*!\n* Quill Editor v1.3.3\n* https://quilljs.com/\n* Copyright (c) 2014, <PERSON>\n* Copyright (c) 2013, salesforce.com\n*/\n    .ql-container {\n        box-sizing: border-box;\n        font-family: Helvetica, Arial, sans-serif;\n        font-size: 13px;\n        height: 100%;\n        margin: 0;\n        position: relative;\n    }\n    .ql-container.ql-disabled .ql-tooltip {\n        visibility: hidden;\n    }\n    .ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {\n        pointer-events: none;\n    }\n    .ql-clipboard {\n        inset-inline-start: -100000px;\n        height: 1px;\n        overflow-y: hidden;\n        position: absolute;\n        top: 50%;\n    }\n    .ql-clipboard p {\n        margin: 0;\n        padding: 0;\n    }\n    .ql-editor {\n        box-sizing: border-box;\n        line-height: 1.42;\n        height: 100%;\n        outline: none;\n        overflow-y: auto;\n        padding: 12px 15px;\n        tab-size: 4;\n        -moz-tab-size: 4;\n        text-align: left;\n        white-space: pre-wrap;\n        word-wrap: break-word;\n    }\n    .ql-editor > * {\n        cursor: text;\n    }\n    .ql-editor p,\n    .ql-editor ol,\n    .ql-editor ul,\n    .ql-editor pre,\n    .ql-editor blockquote,\n    .ql-editor h1,\n    .ql-editor h2,\n    .ql-editor h3,\n    .ql-editor h4,\n    .ql-editor h5,\n    .ql-editor h6 {\n        margin: 0;\n        padding: 0;\n        counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol,\n    .ql-editor ul {\n        padding-inline-start: 1.5rem;\n    }\n    .ql-editor ol > li,\n    .ql-editor ul > li {\n        list-style-type: none;\n    }\n    .ql-editor ul > li::before {\n        content: '\\\\2022';\n    }\n    .ql-editor ul[data-checked='true'],\n    .ql-editor ul[data-checked='false'] {\n        pointer-events: none;\n    }\n    .ql-editor ul[data-checked='true'] > li *,\n    .ql-editor ul[data-checked='false'] > li * {\n        pointer-events: all;\n    }\n    .ql-editor ul[data-checked='true'] > li::before,\n    .ql-editor ul[data-checked='false'] > li::before {\n        color: #777;\n        cursor: pointer;\n        pointer-events: all;\n    }\n    .ql-editor ul[data-checked='true'] > li::before {\n        content: '\\\\2611';\n    }\n    .ql-editor ul[data-checked='false'] > li::before {\n        content: '\\\\2610';\n    }\n    .ql-editor li::before {\n        display: inline-block;\n        white-space: nowrap;\n        width: 1.2rem;\n    }\n    .ql-editor li:not(.ql-direction-rtl)::before {\n        margin-inline-start: -1.5rem;\n        margin-inline-end: 0.3rem;\n        text-align: right;\n    }\n    .ql-editor li.ql-direction-rtl::before {\n        margin-inline-start: 0.3rem;\n        margin-inline-end: -1.5rem;\n    }\n    .ql-editor ol li:not(.ql-direction-rtl),\n    .ql-editor ul li:not(.ql-direction-rtl) {\n        padding-inline-start: 1.5rem;\n    }\n    .ql-editor ol li.ql-direction-rtl,\n    .ql-editor ul li.ql-direction-rtl {\n        padding-inline-end: 1.5rem;\n    }\n    .ql-editor ol li {\n        counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n        counter-increment: list-0;\n    }\n    .ql-editor ol li:before {\n        content: counter(list-0, decimal) '. ';\n    }\n    .ql-editor ol li.ql-indent-1 {\n        counter-increment: list-1;\n    }\n    .ql-editor ol li.ql-indent-1:before {\n        content: counter(list-1, lower-alpha) '. ';\n    }\n    .ql-editor ol li.ql-indent-1 {\n        counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-2 {\n        counter-increment: list-2;\n    }\n    .ql-editor ol li.ql-indent-2:before {\n        content: counter(list-2, lower-roman) '. ';\n    }\n    .ql-editor ol li.ql-indent-2 {\n        counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-3 {\n        counter-increment: list-3;\n    }\n    .ql-editor ol li.ql-indent-3:before {\n        content: counter(list-3, decimal) '. ';\n    }\n    .ql-editor ol li.ql-indent-3 {\n        counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-4 {\n        counter-increment: list-4;\n    }\n    .ql-editor ol li.ql-indent-4:before {\n        content: counter(list-4, lower-alpha) '. ';\n    }\n    .ql-editor ol li.ql-indent-4 {\n        counter-reset: list-5 list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-5 {\n        counter-increment: list-5;\n    }\n    .ql-editor ol li.ql-indent-5:before {\n        content: counter(list-5, lower-roman) '. ';\n    }\n    .ql-editor ol li.ql-indent-5 {\n        counter-reset: list-6 list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-6 {\n        counter-increment: list-6;\n    }\n    .ql-editor ol li.ql-indent-6:before {\n        content: counter(list-6, decimal) '. ';\n    }\n    .ql-editor ol li.ql-indent-6 {\n        counter-reset: list-7 list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-7 {\n        counter-increment: list-7;\n    }\n    .ql-editor ol li.ql-indent-7:before {\n        content: counter(list-7, lower-alpha) '. ';\n    }\n    .ql-editor ol li.ql-indent-7 {\n        counter-reset: list-8 list-9;\n    }\n    .ql-editor ol li.ql-indent-8 {\n        counter-increment: list-8;\n    }\n    .ql-editor ol li.ql-indent-8:before {\n        content: counter(list-8, lower-roman) '. ';\n    }\n    .ql-editor ol li.ql-indent-8 {\n        counter-reset: list-9;\n    }\n    .ql-editor ol li.ql-indent-9 {\n        counter-increment: list-9;\n    }\n    .ql-editor ol li.ql-indent-9:before {\n        content: counter(list-9, decimal) '. ';\n    }\n    .ql-editor .ql-video {\n        display: block;\n        max-width: 100%;\n    }\n    .ql-editor .ql-video.ql-align-center {\n        margin: 0 auto;\n    }\n    .ql-editor .ql-video.ql-align-right {\n        margin: 0 0 0 auto;\n    }\n    .ql-editor .ql-bg-black {\n        background: #000;\n    }\n    .ql-editor .ql-bg-red {\n        background: #e60000;\n    }\n    .ql-editor .ql-bg-orange {\n        background: #f90;\n    }\n    .ql-editor .ql-bg-yellow {\n        background: #ff0;\n    }\n    .ql-editor .ql-bg-green {\n        background: #008a00;\n    }\n    .ql-editor .ql-bg-blue {\n        background: #06c;\n    }\n    .ql-editor .ql-bg-purple {\n        background: #93f;\n    }\n    .ql-editor .ql-color-white {\n        color: #fff;\n    }\n    .ql-editor .ql-color-red {\n        color: #e60000;\n    }\n    .ql-editor .ql-color-orange {\n        color: #f90;\n    }\n    .ql-editor .ql-color-yellow {\n        color: #ff0;\n    }\n    .ql-editor .ql-color-green {\n        color: #008a00;\n    }\n    .ql-editor .ql-color-blue {\n        color: #06c;\n    }\n    .ql-editor .ql-color-purple {\n        color: #93f;\n    }\n    .ql-editor .ql-font-serif {\n        font-family:\n            Georgia,\n            Times New Roman,\n            serif;\n    }\n    .ql-editor .ql-font-monospace {\n        font-family:\n            Monaco,\n            Courier New,\n            monospace;\n    }\n    .ql-editor .ql-size-small {\n        font-size: 0.75rem;\n    }\n    .ql-editor .ql-size-large {\n        font-size: 1.5rem;\n    }\n    .ql-editor .ql-size-huge {\n        font-size: 2.5rem;\n    }\n    .ql-editor .ql-direction-rtl {\n        direction: rtl;\n        text-align: inherit;\n    }\n    .ql-editor .ql-align-center {\n        text-align: center;\n    }\n    .ql-editor .ql-align-justify {\n        text-align: justify;\n    }\n    .ql-editor .ql-align-right {\n        text-align: right;\n    }\n    .ql-editor.ql-blank::before {\n        color: dt('form.field.placeholder.color');\n        content: attr(data-placeholder);\n        font-style: italic;\n        inset-inline-start: 15px;\n        pointer-events: none;\n        position: absolute;\n        inset-inline-end: 15px;\n    }\n    .ql-snow.ql-toolbar:after,\n    .ql-snow .ql-toolbar:after {\n        clear: both;\n        content: '';\n        display: table;\n    }\n    .ql-snow.ql-toolbar button,\n    .ql-snow .ql-toolbar button {\n        background: none;\n        border: none;\n        cursor: pointer;\n        display: inline-block;\n        float: left;\n        height: 24px;\n        padding-block: 3px;\n        padding-inline: 5px;\n        width: 28px;\n    }\n    .ql-snow.ql-toolbar button svg,\n    .ql-snow .ql-toolbar button svg {\n        float: left;\n        height: 100%;\n    }\n    .ql-snow.ql-toolbar button:active:hover,\n    .ql-snow .ql-toolbar button:active:hover {\n        outline: none;\n    }\n    .ql-snow.ql-toolbar input.ql-image[type='file'],\n    .ql-snow .ql-toolbar input.ql-image[type='file'] {\n        display: none;\n    }\n    .ql-snow.ql-toolbar button:hover,\n    .ql-snow .ql-toolbar button:hover,\n    .ql-snow.ql-toolbar button:focus,\n    .ql-snow .ql-toolbar button:focus,\n    .ql-snow.ql-toolbar button.ql-active,\n    .ql-snow .ql-toolbar button.ql-active,\n    .ql-snow.ql-toolbar .ql-picker-label:hover,\n    .ql-snow .ql-toolbar .ql-picker-label:hover,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active,\n    .ql-snow.ql-toolbar .ql-picker-item:hover,\n    .ql-snow .ql-toolbar .ql-picker-item:hover,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected {\n        color: #06c;\n    }\n    .ql-snow.ql-toolbar button:hover .ql-fill,\n    .ql-snow .ql-toolbar button:hover .ql-fill,\n    .ql-snow.ql-toolbar button:focus .ql-fill,\n    .ql-snow .ql-toolbar button:focus .ql-fill,\n    .ql-snow.ql-toolbar button.ql-active .ql-fill,\n    .ql-snow .ql-toolbar button.ql-active .ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n    .ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {\n        fill: #06c;\n    }\n    .ql-snow.ql-toolbar button:hover .ql-stroke,\n    .ql-snow .ql-toolbar button:hover .ql-stroke,\n    .ql-snow.ql-toolbar button:focus .ql-stroke,\n    .ql-snow .ql-toolbar button:focus .ql-stroke,\n    .ql-snow.ql-toolbar button.ql-active .ql-stroke,\n    .ql-snow .ql-toolbar button.ql-active .ql-stroke,\n    .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,\n    .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n    .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,\n    .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n    .ql-snow.ql-toolbar button:hover .ql-stroke-miter,\n    .ql-snow .ql-toolbar button:hover .ql-stroke-miter,\n    .ql-snow.ql-toolbar button:focus .ql-stroke-miter,\n    .ql-snow .ql-toolbar button:focus .ql-stroke-miter,\n    .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,\n    .ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,\n    .ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n    .ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n    .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n    .ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n    .ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n    .ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n    .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,\n    .ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {\n        stroke: #06c;\n    }\n    @media (pointer: coarse) {\n        .ql-snow.ql-toolbar button:hover:not(.ql-active),\n        .ql-snow .ql-toolbar button:hover:not(.ql-active) {\n            color: #444;\n        }\n        .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,\n        .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,\n        .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,\n        .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {\n            fill: #444;\n        }\n        .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n        .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n        .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,\n        .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {\n            stroke: #444;\n        }\n    }\n    .ql-snow {\n        box-sizing: border-box;\n    }\n    .ql-snow * {\n        box-sizing: border-box;\n    }\n    .ql-snow .ql-hidden {\n        display: none;\n    }\n    .ql-snow .ql-out-bottom,\n    .ql-snow .ql-out-top {\n        visibility: hidden;\n    }\n    .ql-snow .ql-tooltip {\n        position: absolute;\n        transform: translateY(10px);\n    }\n    .ql-snow .ql-tooltip a {\n        cursor: pointer;\n        text-decoration: none;\n    }\n    .ql-snow .ql-tooltip.ql-flip {\n        transform: translateY(-10px);\n    }\n    .ql-snow .ql-formats {\n        display: inline-block;\n        vertical-align: middle;\n    }\n    .ql-snow .ql-formats:after {\n        clear: both;\n        content: '';\n        display: table;\n    }\n    .ql-snow .ql-stroke {\n        fill: none;\n        stroke: #444;\n        stroke-linecap: round;\n        stroke-linejoin: round;\n        stroke-width: 2;\n    }\n    .ql-snow .ql-stroke-miter {\n        fill: none;\n        stroke: #444;\n        stroke-miterlimit: 10;\n        stroke-width: 2;\n    }\n    .ql-snow .ql-fill,\n    .ql-snow .ql-stroke.ql-fill {\n        fill: #444;\n    }\n    .ql-snow .ql-empty {\n        fill: none;\n    }\n    .ql-snow .ql-even {\n        fill-rule: evenodd;\n    }\n    .ql-snow .ql-thin,\n    .ql-snow .ql-stroke.ql-thin {\n        stroke-width: 1;\n    }\n    .ql-snow .ql-transparent {\n        opacity: 0.4;\n    }\n    .ql-snow .ql-direction svg:last-child {\n        display: none;\n    }\n    .ql-snow .ql-direction.ql-active svg:last-child {\n        display: inline;\n    }\n    .ql-snow .ql-direction.ql-active svg:first-child {\n        display: none;\n    }\n    .ql-snow .ql-editor h1 {\n        font-size: 2rem;\n    }\n    .ql-snow .ql-editor h2 {\n        font-size: 1.5rem;\n    }\n    .ql-snow .ql-editor h3 {\n        font-size: 1.17rem;\n    }\n    .ql-snow .ql-editor h4 {\n        font-size: 1rem;\n    }\n    .ql-snow .ql-editor h5 {\n        font-size: 0.83rem;\n    }\n    .ql-snow .ql-editor h6 {\n        font-size: 0.67rem;\n    }\n    .ql-snow .ql-editor a {\n        text-decoration: underline;\n    }\n    .ql-snow .ql-editor blockquote {\n        border-inline-start: 4px solid #ccc;\n        margin-block-end: 5px;\n        margin-block-start: 5px;\n        padding-inline-start: 16px;\n    }\n    .ql-snow .ql-editor code,\n    .ql-snow .ql-editor pre {\n        background: #f0f0f0;\n        border-radius: 3px;\n    }\n    .ql-snow .ql-editor pre {\n        white-space: pre-wrap;\n        margin-block-end: 5px;\n        margin-block-start: 5px;\n        padding: 5px 10px;\n    }\n    .ql-snow .ql-editor code {\n        font-size: 85%;\n        padding: 2px 4px;\n    }\n    .ql-snow .ql-editor pre.ql-syntax {\n        background: #23241f;\n        color: #f8f8f2;\n        overflow: visible;\n    }\n    .ql-snow .ql-editor img {\n        max-width: 100%;\n    }\n    .ql-snow .ql-picker {\n        color: #444;\n        display: inline-block;\n        float: left;\n        inset-inline-start: 0;\n        font-size: 14px;\n        font-weight: 500;\n        height: 24px;\n        position: relative;\n        vertical-align: middle;\n    }\n    .ql-snow .ql-picker-label {\n        cursor: pointer;\n        display: inline-block;\n        height: 100%;\n        padding-inline-start: 8px;\n        padding-inline-end: 2px;\n        position: relative;\n        width: 100%;\n    }\n    .ql-snow .ql-picker-label::before {\n        display: inline-block;\n        line-height: 22px;\n    }\n    .ql-snow .ql-picker-options {\n        background: #fff;\n        display: none;\n        min-width: 100%;\n        padding: 4px 8px;\n        position: absolute;\n        white-space: nowrap;\n    }\n    .ql-snow .ql-picker-options .ql-picker-item {\n        cursor: pointer;\n        display: block;\n        padding-block-end: 5px;\n        padding-block-start: 5px;\n    }\n    .ql-snow .ql-picker.ql-expanded .ql-picker-label {\n        color: #ccc;\n        z-index: 2;\n    }\n    .ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n        fill: #ccc;\n    }\n    .ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n        stroke: #ccc;\n    }\n    .ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        display: block;\n        margin-block-start: -1px;\n        top: 100%;\n        z-index: 1;\n    }\n    .ql-snow .ql-color-picker,\n    .ql-snow .ql-icon-picker {\n        width: 28px;\n    }\n    .ql-snow .ql-color-picker .ql-picker-label,\n    .ql-snow .ql-icon-picker .ql-picker-label {\n        padding: 2px 4px;\n    }\n    .ql-snow .ql-color-picker .ql-picker-label svg,\n    .ql-snow .ql-icon-picker .ql-picker-label svg {\n        inset-inline-end: 4px;\n    }\n    .ql-snow .ql-icon-picker .ql-picker-options {\n        padding: 4px 0;\n    }\n    .ql-snow .ql-icon-picker .ql-picker-item {\n        height: 24px;\n        width: 24px;\n        padding: 2px 4px;\n    }\n    .ql-snow .ql-color-picker .ql-picker-options {\n        padding: 3px 5px;\n        width: 152px;\n    }\n    .ql-snow .ql-color-picker .ql-picker-item {\n        border: 1px solid transparent;\n        float: left;\n        height: 16px;\n        margin: 2px;\n        padding: 0;\n        width: 16px;\n    }\n    .ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n        position: absolute;\n        margin-block-start: -9px;\n        inset-inline-end: 0;\n        top: 50%;\n        width: 18px;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,\n    .ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,\n    .ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {\n        content: attr(data-label);\n    }\n    .ql-snow .ql-picker.ql-header {\n        width: 98px;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item::before {\n        content: 'Normal';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {\n        content: 'Heading 1';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {\n        content: 'Heading 2';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {\n        content: 'Heading 3';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {\n        content: 'Heading 4';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {\n        content: 'Heading 5';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {\n        content: 'Heading 6';\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {\n        font-size: 2rem;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {\n        font-size: 1.5rem;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {\n        font-size: 1.17rem;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {\n        font-size: 1rem;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {\n        font-size: 0.83rem;\n    }\n    .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {\n        font-size: 0.67rem;\n    }\n    .ql-snow .ql-picker.ql-font {\n        width: 108px;\n    }\n    .ql-snow .ql-picker.ql-font .ql-picker-label::before,\n    .ql-snow .ql-picker.ql-font .ql-picker-item::before {\n        content: 'Sans Serif';\n    }\n    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,\n    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {\n        content: 'Serif';\n    }\n    .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,\n    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {\n        content: 'Monospace';\n    }\n    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {\n        font-family:\n            Georgia,\n            Times New Roman,\n            serif;\n    }\n    .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {\n        font-family:\n            Monaco,\n            Courier New,\n            monospace;\n    }\n    .ql-snow .ql-picker.ql-size {\n        width: 98px;\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-label::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-item::before {\n        content: 'Normal';\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {\n        content: 'Small';\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {\n        content: 'Large';\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {\n        content: 'Huge';\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {\n        font-size: 10px;\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {\n        font-size: 18px;\n    }\n    .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {\n        font-size: 32px;\n    }\n    .ql-snow .ql-color-picker.ql-background .ql-picker-item {\n        background: #fff;\n    }\n    .ql-snow .ql-color-picker.ql-color .ql-picker-item {\n        background: #000;\n    }\n    .ql-toolbar.ql-snow {\n        border: 1px solid #ccc;\n        box-sizing: border-box;\n        font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;\n        padding: 8px;\n    }\n    .ql-toolbar.ql-snow .ql-formats {\n        margin-inline-end: 15px;\n    }\n    .ql-toolbar.ql-snow .ql-picker-label {\n        border: 1px solid transparent;\n    }\n    .ql-toolbar.ql-snow .ql-picker-options {\n        border: 1px solid transparent;\n        box-shadow: rgba(0, 0, 0, 0.2) 0 2px 8px;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n        border-color: #ccc;\n    }\n    .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        border-color: #ccc;\n    }\n    .ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,\n    .ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {\n        border-color: #000;\n    }\n    .ql-toolbar.ql-snow + .ql-container.ql-snow {\n        border-block-start: 0;\n    }\n    .ql-snow .ql-tooltip {\n        background: #fff;\n        border: 1px solid #ccc;\n        box-shadow: 0 0 5px #ddd;\n        color: #444;\n        padding: 5px 12px;\n        white-space: nowrap;\n    }\n    .ql-snow .ql-tooltip::before {\n        content: 'Visit URL:';\n        line-height: 26px;\n        margin-inline-end: 8px;\n    }\n    .ql-snow .ql-tooltip input[type='text'] {\n        display: none;\n        border: 1px solid #ccc;\n        font-size: 13px;\n        height: 26px;\n        margin: 0;\n        padding: 3px 5px;\n        width: 170px;\n    }\n    .ql-snow .ql-tooltip a.ql-preview {\n        display: inline-block;\n        max-width: 200px;\n        overflow-x: hidden;\n        text-overflow: ellipsis;\n        vertical-align: top;\n    }\n    .ql-snow .ql-tooltip a.ql-action::after {\n        border-inline-end: 1px solid #ccc;\n        content: 'Edit';\n        margin-inline-start: 16px;\n        padding-inline-end: 8px;\n    }\n    .ql-snow .ql-tooltip a.ql-remove::before {\n        content: 'Remove';\n        margin-inline-start: 8px;\n    }\n    .ql-snow .ql-tooltip a {\n        line-height: 26px;\n    }\n    .ql-snow .ql-tooltip.ql-editing a.ql-preview,\n    .ql-snow .ql-tooltip.ql-editing a.ql-remove {\n        display: none;\n    }\n    .ql-snow .ql-tooltip.ql-editing input[type='text'] {\n        display: inline-block;\n    }\n    .ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n        border-inline-end: 0;\n        content: 'Save';\n        padding-inline-end: 0;\n    }\n    .ql-snow .ql-tooltip[data-mode='link']::before {\n        content: 'Enter link:';\n    }\n    .ql-snow .ql-tooltip[data-mode='formula']::before {\n        content: 'Enter formula:';\n    }\n    .ql-snow .ql-tooltip[data-mode='video']::before {\n        content: 'Enter video:';\n    }\n    .ql-snow a {\n        color: #06c;\n    }\n    .ql-container.ql-snow {\n        border: 1px solid #ccc;\n    }\n\n    .p-editor {\n        display: block;\n    }\n\n    .p-editor .p-editor-toolbar {\n        background: dt('editor.toolbar.background');\n        border-start-end-radius: dt('editor.toolbar.border.radius');\n        border-start-start-radius: dt('editor.toolbar.border.radius');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow {\n        border: 1px solid dt('editor.toolbar.border.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-stroke {\n        stroke: dt('editor.toolbar.item.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-fill {\n        fill: dt('editor.toolbar.item.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label {\n        border: 0 none;\n        color: dt('editor.toolbar.item.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover {\n        color: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-stroke {\n        stroke: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-fill {\n        fill: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n        color: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n        stroke: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n        fill: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n        background: dt('editor.overlay.background');\n        border: 1px solid dt('editor.overlay.border.color');\n        box-shadow: dt('editor.overlay.shadow');\n        border-radius: dt('editor.overlay.border.radius');\n        padding: dt('editor.overlay.padding');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item {\n        color: dt('editor.overlay.option.color');\n        border-radius: dt('editor.overlay.option.border.radius');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item:hover {\n        background: dt('editor.overlay.option.focus.background');\n        color: dt('editor.overlay.option.focus.color');\n    }\n\n    .p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded:not(.ql-color-picker, .ql-icon-picker) .ql-picker-item {\n        padding: dt('editor.overlay.option.padding');\n    }\n\n    .p-editor .p-editor-content {\n        border-end-end-radius: dt('editor.content.border.radius');\n        border-end-start-radius: dt('editor.content.border.radius');\n    }\n\n    .p-editor .p-editor-content.ql-snow {\n        border: 1px solid dt('editor.content.border.color');\n    }\n\n    .p-editor .p-editor-content .ql-editor {\n        background: dt('editor.content.background');\n        color: dt('editor.content.color');\n        border-end-end-radius: dt('editor.content.border.radius');\n        border-end-start-radius: dt('editor.content.border.radius');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button:hover,\n    .p-editor .ql-snow.ql-toolbar button:focus {\n        color: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button:hover .ql-stroke,\n    .p-editor .ql-snow.ql-toolbar button:focus .ql-stroke {\n        stroke: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button:hover .ql-fill,\n    .p-editor .ql-snow.ql-toolbar button:focus .ql-fill {\n        fill: dt('editor.toolbar.item.hover.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button.ql-active,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected {\n        color: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke {\n        stroke: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button.ql-active .ql-fill,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill {\n        fill: dt('editor.toolbar.item.active.color');\n    }\n\n    .p-editor .ql-snow.ql-toolbar button.ql-active .ql-picker-label,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-picker-label,\n    .p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-picker-label {\n        color: dt('editor.toolbar.item.active.color');\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}