{"version": 3, "sources": ["../../src/orderlist/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-orderlist {\n        display: flex;\n        gap: dt('orderlist.gap');\n    }\n\n    .p-orderlist .p-listbox {\n        flex: 1 1 auto;\n    }\n\n    .p-orderlist-controls {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        gap: dt('orderlist.controls.gap');\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}