{"version": 3, "sources": ["../../src/knob/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-knob-range {\n        fill: none;\n        transition: stroke 0.1s ease-in;\n    }\n\n    .p-knob-value {\n        animation-name: p-knob-dash-frame;\n        animation-fill-mode: forwards;\n        fill: none;\n    }\n\n    .p-knob-text {\n        font-size: 1.3rem;\n        text-align: center;\n    }\n\n    .p-knob svg {\n        border-radius: 50%;\n        outline-color: transparent;\n        transition:\n            background dt('knob.transition.duration'),\n            color dt('knob.transition.duration'),\n            outline-color dt('knob.transition.duration'),\n            box-shadow dt('knob.transition.duration');\n    }\n\n    .p-knob svg:focus-visible {\n        box-shadow: dt('knob.focus.ring.shadow');\n        outline: dt('knob.focus.ring.width') dt('knob.focus.ring.style') dt('knob.focus.ring.color');\n        outline-offset: dt('knob.focus.ring.offset');\n    }\n\n    @keyframes p-knob-dash-frame {\n        100% {\n            stroke-dashoffset: 0;\n        }\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}