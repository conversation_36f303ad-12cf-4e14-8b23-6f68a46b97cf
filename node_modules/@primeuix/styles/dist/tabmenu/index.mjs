var style="\n    .p-tabmenu {\n        overflow-x: auto;\n    }\n\n    .p-tabmenu-tablist {\n        display: flex;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n        background: dt('tabmenu.tablist.background');\n        border-style: solid;\n        border-color: dt('tabmenu.tablist.border.color');\n        border-width: dt('tabmenu.tablist.border.width');\n        position: relative;\n    }\n\n    .p-tabmenu-item-link {\n        cursor: pointer;\n        user-select: none;\n        display: flex;\n        align-items: center;\n        text-decoration: none;\n        position: relative;\n        overflow: hidden;\n        background: dt('tabmenu.item.background');\n        border-style: solid;\n        border-width: dt('tabmenu.item.border.width');\n        border-color: dt('tabmenu.item.border.color');\n        color: dt('tabmenu.item.color');\n        padding: dt('tabmenu.item.padding');\n        font-weight: dt('tabmenu.item.font.weight');\n        transition:\n            background dt('tabmenu.transition.duration'),\n            border-color dt('tabmenu.transition.duration'),\n            color dt('tabmenu.transition.duration'),\n            outline-color dt('tabmenu.transition.duration'),\n            box-shadow dt('tabmenu.transition.duration');\n        margin: dt('tabmenu.item.margin');\n        outline-color: transparent;\n        gap: dt('tabmenu.item.gap');\n    }\n\n    .p-tabmenu-item-link:focus-visible {\n        z-index: 1;\n        box-shadow: dt('tabmenu.item.focus.ring.shadow');\n        outline: dt('tabmenu.item.focus.ring.width') dt('tabmenu.item.focus.ring.style') dt('tabmenu.item.focus.ring.color');\n        outline-offset: dt('tabmenu.item.focus.ring.offset');\n    }\n\n    .p-tabmenu-item-icon {\n        color: dt('tabmenu.item.icon.color');\n        transition:\n            background dt('tabmenu.transition.duration'),\n            border-color dt('tabmenu.transition.duration'),\n            color dt('tabmenu.transition.duration'),\n            outline-color dt('tabmenu.transition.duration'),\n            box-shadow dt('tabmenu.transition.duration');\n    }\n\n    .p-tabmenu-item-label {\n        line-height: 1;\n    }\n\n    .p-tabmenu-item:not(.p-tabmenu-item-active):not(.p-disabled):hover .p-tabmenu-item-link {\n        background: dt('tabmenu.item.hover.background');\n        border-color: dt('tabmenu.item.hover.border.color');\n        color: dt('tabmenu.item.hover.color');\n    }\n\n    .p-tabmenu-item:not(.p-tabmenu-item-active):not(.p-disabled):hover .p-tabmenu-item-icon {\n        color: dt('tabmenu.item.icon.hover.color');\n    }\n\n    .p-tabmenu-item-active .p-tabmenu-item-link {\n        background: dt('tabmenu.item.active.background');\n        border-color: dt('tabmenu.item.active.border.color');\n        color: dt('tabmenu.item.active.color');\n    }\n\n    .p-tabmenu-item-active .p-tabmenu-item-icon {\n        color: dt('tabmenu.item.icon.active.color');\n    }\n\n    .p-tabmenu-active-bar {\n        z-index: 1;\n        display: block;\n        position: absolute;\n        bottom: dt('tabmenu.active.bar.bottom');\n        height: dt('tabmenu.active.bar.height');\n        background: dt('tabmenu.active.bar.background');\n        transition: 250ms cubic-bezier(0.35, 0, 0.25, 1);\n    }\n\n    .p-tabmenu::-webkit-scrollbar {\n        display: none;\n    }\n";export{style};//# sourceMappingURL=index.mjs.map