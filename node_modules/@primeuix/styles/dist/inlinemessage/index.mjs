var style="\n    .p-inlinemessage {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        padding: dt('inlinemessage.padding');\n        border-radius: dt('inlinemessage.border.radius');\n        gap: dt('inlinemessage.gap');\n    }\n\n    .p-inlinemessage-text {\n        font-weight: dt('inlinemessage.text.font.weight');\n    }\n\n    .p-inlinemessage-icon {\n        flex-shrink: 0;\n        font-size: dt('inlinemessage.icon.size');\n        width: dt('inlinemessage.icon.size');\n        height: dt('inlinemessage.icon.size');\n    }\n\n    .p-inlinemessage-icon-only .p-inlinemessage-text {\n        visibility: hidden;\n        width: 0;\n    }\n\n    .p-inlinemessage-info {\n        background: dt('inlinemessage.info.background');\n        border: 1px solid dt('inlinemessage.info.border.color');\n        color: dt('inlinemessage.info.color');\n        box-shadow: dt('inlinemessage.info.shadow');\n    }\n\n    .p-inlinemessage-info .p-inlinemessage-icon {\n        color: dt('inlinemessage.info.color');\n    }\n\n    .p-inlinemessage-success {\n        background: dt('inlinemessage.success.background');\n        border: 1px solid dt('inlinemessage.success.border.color');\n        color: dt('inlinemessage.success.color');\n        box-shadow: dt('inlinemessage.success.shadow');\n    }\n\n    .p-inlinemessage-success .p-inlinemessage-icon {\n        color: dt('inlinemessage.success.color');\n    }\n\n    .p-inlinemessage-warn {\n        background: dt('inlinemessage.warn.background');\n        border: 1px solid dt('inlinemessage.warn.border.color');\n        color: dt('inlinemessage.warn.color');\n        box-shadow: dt('inlinemessage.warn.shadow');\n    }\n\n    .p-inlinemessage-warn .p-inlinemessage-icon {\n        color: dt('inlinemessage.warn.color');\n    }\n\n    .p-inlinemessage-error {\n        background: dt('inlinemessage.error.background');\n        border: 1px solid dt('inlinemessage.error.border.color');\n        color: dt('inlinemessage.error.color');\n        box-shadow: dt('inlinemessage.error.shadow');\n    }\n\n    .p-inlinemessage-error .p-inlinemessage-icon {\n        color: dt('inlinemessage.error.color');\n    }\n\n    .p-inlinemessage-secondary {\n        background: dt('inlinemessage.secondary.background');\n        border: 1px solid dt('inlinemessage.secondary.border.color');\n        color: dt('inlinemessage.secondary.color');\n        box-shadow: dt('inlinemessage.secondary.shadow');\n    }\n\n    .p-inlinemessage-secondary .p-inlinemessage-icon {\n        color: dt('inlinemessage.secondary.color');\n    }\n\n    .p-inlinemessage-contrast {\n        background: dt('inlinemessage.contrast.background');\n        border: 1px solid dt('inlinemessage.contrast.border.color');\n        color: dt('inlinemessage.contrast.color');\n        box-shadow: dt('inlinemessage.contrast.shadow');\n    }\n\n    .p-inlinemessage-contrast .p-inlinemessage-icon {\n        color: dt('inlinemessage.contrast.color');\n    }\n";export{style};//# sourceMappingURL=index.mjs.map