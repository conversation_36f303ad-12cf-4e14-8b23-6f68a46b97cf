{"version": 3, "sources": ["../../src/listbox/index.ts"], "sourcesContent": ["export const style = /*css*/ `\n    .p-listbox {\n        display: block;\n        background: dt('listbox.background');\n        color: dt('listbox.color');\n        border: 1px solid dt('listbox.border.color');\n        border-radius: dt('listbox.border.radius');\n        transition:\n            background dt('listbox.transition.duration'),\n            color dt('listbox.transition.duration'),\n            border-color dt('listbox.transition.duration'),\n            box-shadow dt('listbox.transition.duration'),\n            outline-color dt('listbox.transition.duration');\n        outline-color: transparent;\n        box-shadow: dt('listbox.shadow');\n    }\n\n    .p-listbox.p-disabled {\n        opacity: 1;\n        background: dt('listbox.disabled.background');\n        color: dt('listbox.disabled.color');\n    }\n\n    .p-listbox.p-disabled .p-listbox-option {\n        color: dt('listbox.disabled.color');\n    }\n\n    .p-listbox.p-invalid {\n        border-color: dt('listbox.invalid.border.color');\n    }\n\n    .p-listbox-header {\n        padding: dt('listbox.list.header.padding');\n    }\n\n    .p-listbox-filter {\n        width: 100%;\n    }\n\n    .p-listbox-list-container {\n        overflow: auto;\n    }\n\n    .p-listbox-list {\n        list-style-type: none;\n        margin: 0;\n        padding: dt('listbox.list.padding');\n        outline: 0 none;\n        display: flex;\n        flex-direction: column;\n        gap: dt('listbox.list.gap');\n    }\n\n    .p-listbox-option {\n        display: flex;\n        align-items: center;\n        cursor: pointer;\n        position: relative;\n        overflow: hidden;\n        padding: dt('listbox.option.padding');\n        border: 0 none;\n        border-radius: dt('listbox.option.border.radius');\n        color: dt('listbox.option.color');\n        transition:\n            background dt('listbox.transition.duration'),\n            color dt('listbox.transition.duration'),\n            border-color dt('listbox.transition.duration'),\n            box-shadow dt('listbox.transition.duration'),\n            outline-color dt('listbox.transition.duration');\n    }\n\n    .p-listbox-striped li:nth-child(even of .p-listbox-option) {\n        background: dt('listbox.option.striped.background');\n    }\n\n    .p-listbox .p-listbox-list .p-listbox-option.p-listbox-option-selected {\n        background: dt('listbox.option.selected.background');\n        color: dt('listbox.option.selected.color');\n    }\n\n    .p-listbox:not(.p-disabled) .p-listbox-option.p-listbox-option-selected.p-focus {\n        background: dt('listbox.option.selected.focus.background');\n        color: dt('listbox.option.selected.focus.color');\n    }\n\n    .p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled).p-focus {\n        background: dt('listbox.option.focus.background');\n        color: dt('listbox.option.focus.color');\n    }\n\n    .p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled):hover {\n        background: dt('listbox.option.focus.background');\n        color: dt('listbox.option.focus.color');\n    }\n\n    .p-listbox-option-blank-icon {\n        flex-shrink: 0;\n    }\n\n    .p-listbox-option-check-icon {\n        position: relative;\n        flex-shrink: 0;\n        margin-inline-start: dt('listbox.checkmark.gutter.start');\n        margin-inline-end: dt('listbox.checkmark.gutter.end');\n        color: dt('listbox.checkmark.color');\n    }\n\n    .p-listbox-option-group {\n        margin: 0;\n        padding: dt('listbox.option.group.padding');\n        color: dt('listbox.option.group.color');\n        background: dt('listbox.option.group.background');\n        font-weight: dt('listbox.option.group.font.weight');\n    }\n\n    .p-listbox-empty-message {\n        padding: dt('listbox.empty.message.padding');\n    }\n\n    .p-listbox-fluid {\n        width: 100%;\n    }\n`;\n"], "mappings": ";AAAO,IAAM;AAAA;AAAA,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}