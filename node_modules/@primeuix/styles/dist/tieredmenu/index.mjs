var style="\n    .p-tieredmenu {\n        background: dt('tieredmenu.background');\n        color: dt('tieredmenu.color');\n        border: 1px solid dt('tieredmenu.border.color');\n        border-radius: dt('tieredmenu.border.radius');\n        min-width: 12.5rem;\n    }\n\n    .p-tieredmenu-root-list,\n    .p-tieredmenu-submenu {\n        margin: 0;\n        padding: dt('tieredmenu.list.padding');\n        list-style: none;\n        outline: 0 none;\n        display: flex;\n        flex-direction: column;\n        gap: dt('tieredmenu.list.gap');\n    }\n\n    .p-tieredmenu-submenu {\n        position: absolute;\n        min-width: 100%;\n        z-index: 1;\n        background: dt('tieredmenu.background');\n        color: dt('tieredmenu.color');\n        border: 1px solid dt('tieredmenu.border.color');\n        border-radius: dt('tieredmenu.border.radius');\n        box-shadow: dt('tieredmenu.shadow');\n    }\n\n    .p-tieredmenu-item {\n        position: relative;\n    }\n\n    .p-tieredmenu-item-content {\n        transition:\n            background dt('tieredmenu.transition.duration'),\n            color dt('tieredmenu.transition.duration');\n        border-radius: dt('tieredmenu.item.border.radius');\n        color: dt('tieredmenu.item.color');\n    }\n\n    .p-tieredmenu-item-link {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        text-decoration: none;\n        overflow: hidden;\n        position: relative;\n        color: inherit;\n        padding: dt('tieredmenu.item.padding');\n        gap: dt('tieredmenu.item.gap');\n        user-select: none;\n        outline: 0 none;\n    }\n\n    .p-tieredmenu-item-label {\n        line-height: 1;\n    }\n\n    .p-tieredmenu-item-icon {\n        color: dt('tieredmenu.item.icon.color');\n    }\n\n    .p-tieredmenu-submenu-icon {\n        color: dt('tieredmenu.submenu.icon.color');\n        margin-left: auto;\n        font-size: dt('tieredmenu.submenu.icon.size');\n        width: dt('tieredmenu.submenu.icon.size');\n        height: dt('tieredmenu.submenu.icon.size');\n    }\n\n    .p-tieredmenu-submenu-icon:dir(rtl) {\n        margin-left: 0;\n        margin-right: auto;\n    }\n\n    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content {\n        color: dt('tieredmenu.item.focus.color');\n        background: dt('tieredmenu.item.focus.background');\n    }\n\n    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-item-icon {\n        color: dt('tieredmenu.item.icon.focus.color');\n    }\n\n    .p-tieredmenu-item.p-focus > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {\n        color: dt('tieredmenu.submenu.icon.focus.color');\n    }\n\n    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover {\n        color: dt('tieredmenu.item.focus.color');\n        background: dt('tieredmenu.item.focus.background');\n    }\n\n    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-item-icon {\n        color: dt('tieredmenu.item.icon.focus.color');\n    }\n\n    .p-tieredmenu-item:not(.p-disabled) > .p-tieredmenu-item-content:hover .p-tieredmenu-submenu-icon {\n        color: dt('tieredmenu.submenu.icon.focus.color');\n    }\n\n    .p-tieredmenu-item-active > .p-tieredmenu-item-content {\n        color: dt('tieredmenu.item.active.color');\n        background: dt('tieredmenu.item.active.background');\n    }\n\n    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-item-icon {\n        color: dt('tieredmenu.item.icon.active.color');\n    }\n\n    .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {\n        color: dt('tieredmenu.submenu.icon.active.color');\n    }\n\n    .p-tieredmenu-separator {\n        border-block-start: 1px solid dt('tieredmenu.separator.border.color');\n    }\n\n    .p-tieredmenu-overlay {\n        box-shadow: dt('tieredmenu.shadow');\n    }\n\n    .p-tieredmenu-enter-from,\n    .p-tieredmenu-leave-active {\n        opacity: 0;\n    }\n\n    .p-tieredmenu-enter-active {\n        transition: opacity 250ms;\n    }\n\n    .p-tieredmenu-mobile .p-tieredmenu-submenu {\n        position: static;\n        box-shadow: none;\n        border: 0 none;\n        padding-inline-start: dt('tieredmenu.submenu.mobile.indent');\n        padding-inline-end: 0;\n    }\n\n    .p-tieredmenu-mobile .p-tieredmenu-submenu:dir(rtl) {\n        padding-inline-start: 0;\n        padding-inline-end: dt('tieredmenu.submenu.mobile.indent');\n    }\n\n    .p-tieredmenu-mobile .p-tieredmenu-submenu-icon {\n        transition: transform 0.2s;\n        transform: rotate(90deg);\n    }\n\n    .p-tieredmenu-mobile .p-tieredmenu-item-active > .p-tieredmenu-item-content .p-tieredmenu-submenu-icon {\n        transform: rotate(-90deg);\n    }\n";export{style};//# sourceMappingURL=index.mjs.map