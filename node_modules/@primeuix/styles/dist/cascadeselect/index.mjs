var style="\n    .p-cascadeselect {\n        display: inline-flex;\n        cursor: pointer;\n        position: relative;\n        user-select: none;\n        background: dt('cascadeselect.background');\n        border: 1px solid dt('cascadeselect.border.color');\n        transition:\n            background dt('cascadeselect.transition.duration'),\n            color dt('cascadeselect.transition.duration'),\n            border-color dt('cascadeselect.transition.duration'),\n            outline-color dt('cascadeselect.transition.duration'),\n            box-shadow dt('cascadeselect.transition.duration');\n        border-radius: dt('cascadeselect.border.radius');\n        outline-color: transparent;\n        box-shadow: dt('cascadeselect.shadow');\n    }\n\n    .p-cascadeselect:not(.p-disabled):hover {\n        border-color: dt('cascadeselect.hover.border.color');\n    }\n\n    .p-cascadeselect:not(.p-disabled).p-focus {\n        border-color: dt('cascadeselect.focus.border.color');\n        box-shadow: dt('cascadeselect.focus.ring.shadow');\n        outline: dt('cascadeselect.focus.ring.width') dt('cascadeselect.focus.ring.style') dt('cascadeselect.focus.ring.color');\n        outline-offset: dt('cascadeselect.focus.ring.offset');\n    }\n\n    .p-cascadeselect.p-variant-filled {\n        background: dt('cascadeselect.filled.background');\n    }\n\n    .p-cascadeselect.p-variant-filled:not(.p-disabled):hover {\n        background: dt('cascadeselect.filled.hover.background');\n    }\n\n    .p-cascadeselect.p-variant-filled.p-focus {\n        background: dt('cascadeselect.filled.focus.background');\n    }\n\n    .p-cascadeselect.p-invalid {\n        border-color: dt('cascadeselect.invalid.border.color');\n    }\n\n    .p-cascadeselect.p-disabled {\n        opacity: 1;\n        background: dt('cascadeselect.disabled.background');\n    }\n\n    .p-cascadeselect-dropdown {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        flex-shrink: 0;\n        background: transparent;\n        color: dt('cascadeselect.dropdown.color');\n        width: dt('cascadeselect.dropdown.width');\n        border-start-end-radius: dt('border.radius.md');\n        border-end-end-radius: dt('border.radius.md');\n    }\n\n    .p-cascadeselect-clear-icon {\n        align-self: center;\n        color: dt('cascadeselect.clear.icon.color');\n        inset-inline-end: dt('cascadeselect.dropdown.width');\n    }\n\n    .p-cascadeselect-label {\n        display: block;\n        white-space: nowrap;\n        overflow: hidden;\n        flex: 1 1 auto;\n        width: 1%;\n        text-overflow: ellipsis;\n        cursor: pointer;\n        padding: dt('cascadeselect.padding.y') dt('cascadeselect.padding.x');\n        background: transparent;\n        border: 0 none;\n        outline: 0 none;\n    }\n\n    .p-cascadeselect-label.p-placeholder {\n        color: dt('cascadeselect.placeholder.color');\n    }\n\n    .p-cascadeselect.p-invalid .p-cascadeselect-label.p-placeholder {\n        color: dt('cascadeselect.invalid.placeholder.color');\n    }\n\n    .p-cascadeselect.p-disabled .p-cascadeselect-label {\n        color: dt('cascadeselect.disabled.color');\n    }\n\n    .p-cascadeselect-label-empty {\n        overflow: hidden;\n        visibility: hidden;\n    }\n\n    .p-cascadeselect-overlay {\n        background: dt('cascadeselect.overlay.background');\n        color: dt('cascadeselect.overlay.color');\n        border: 1px solid dt('cascadeselect.overlay.border.color');\n        border-radius: dt('cascadeselect.overlay.border.radius');\n        box-shadow: dt('cascadeselect.overlay.shadow');\n    }\n\n    .p-cascadeselect .p-cascadeselect-overlay {\n        min-width: 100%;\n    }\n\n    .p-cascadeselect-option-list {\n        display: none;\n        min-width: 100%;\n        position: absolute;\n        z-index: 1;\n    }\n\n    .p-cascadeselect-list {\n        min-width: 100%;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n        padding: dt('cascadeselect.list.padding');\n        display: flex;\n        flex-direction: column;\n        gap: dt('cascadeselect.list.gap');\n    }\n\n    .p-cascadeselect-option {\n        cursor: pointer;\n        font-weight: normal;\n        white-space: nowrap;\n        border: 0 none;\n        color: dt('cascadeselect.option.color');\n        background: transparent;\n        border-radius: dt('cascadeselect.option.border.radius');\n    }\n\n    .p-cascadeselect-option-active {\n        overflow: visible;\n    }\n\n    .p-cascadeselect-option-active > .p-cascadeselect-option-content {\n        background: dt('cascadeselect.option.focus.background');\n        color: dt('cascadeselect.option.focus.color');\n    }\n\n    .p-cascadeselect-option:not(.p-cascadeselect-option-selected):not(.p-disabled).p-focus > .p-cascadeselect-option-content {\n        background: dt('cascadeselect.option.focus.background');\n        color: dt('cascadeselect.option.focus.color');\n    }\n\n    .p-cascadeselect-option:not(.p-cascadeselect-option-selected):not(.p-disabled).p-focus > .p-cascadeselect-option-content > .p-cascadeselect-group-icon-container > .p-cascadeselect-group-icon {\n        color: dt('cascadeselect.option.icon.focus.color');\n    }\n\n    .p-cascadeselect-option-selected > .p-cascadeselect-option-content {\n        background: dt('cascadeselect.option.selected.background');\n        color: dt('cascadeselect.option.selected.color');\n    }\n\n    .p-cascadeselect-option-selected.p-focus > .p-cascadeselect-option-content {\n        background: dt('cascadeselect.option.selected.focus.background');\n        color: dt('cascadeselect.option.selected.focus.color');\n    }\n\n    .p-cascadeselect-option-active > .p-cascadeselect-option-list {\n        inset-inline-start: 100%;\n        inset-block-start: 0;\n    }\n\n    .p-cascadeselect-option-content {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        overflow: hidden;\n        position: relative;\n        padding: dt('cascadeselect.option.padding');\n        border-radius: dt('cascadeselect.option.border.radius');\n        transition:\n            background dt('cascadeselect.transition.duration'),\n            color dt('cascadeselect.transition.duration'),\n            border-color dt('cascadeselect.transition.duration'),\n            box-shadow dt('cascadeselect.transition.duration'),\n            outline-color dt('cascadeselect.transition.duration');\n    }\n\n    .p-cascadeselect-group-icon {\n        font-size: dt('cascadeselect.option.icon.size');\n        width: dt('cascadeselect.option.icon.size');\n        height: dt('cascadeselect.option.icon.size');\n        color: dt('cascadeselect.option.icon.color');\n    }\n\n    .p-cascadeselect-group-icon:dir(rtl) {\n        transform: rotate(180deg);\n    }\n\n    .p-cascadeselect-mobile-active .p-cascadeselect-option-list {\n        position: static;\n        box-shadow: none;\n        border: 0 none;\n        padding-inline-start: dt('tieredmenu.submenu.mobile.indent');\n        padding-inline-end: 0;\n    }\n\n    .p-cascadeselect-mobile-active .p-cascadeselect-group-icon {\n        transition: transform 0.2s;\n        transform: rotate(90deg);\n    }\n\n    .p-cascadeselect-mobile-active .p-cascadeselect-option-active > .p-cascadeselect-option-content .p-cascadeselect-group-icon {\n        transform: rotate(-90deg);\n    }\n\n    .p-cascadeselect-sm .p-cascadeselect-label {\n        font-size: dt('cascadeselect.sm.font.size');\n        padding-block: dt('cascadeselect.sm.padding.y');\n        padding-inline: dt('cascadeselect.sm.padding.x');\n    }\n\n    .p-cascadeselect-sm .p-cascadeselect-dropdown .p-icon {\n        font-size: dt('cascadeselect.sm.font.size');\n        width: dt('cascadeselect.sm.font.size');\n        height: dt('cascadeselect.sm.font.size');\n    }\n\n    .p-cascadeselect-lg .p-cascadeselect-label {\n        font-size: dt('cascadeselect.lg.font.size');\n        padding-block: dt('cascadeselect.lg.padding.y');\n        padding-inline: dt('cascadeselect.lg.padding.x');\n    }\n\n    .p-cascadeselect-lg .p-cascadeselect-dropdown .p-icon {\n        font-size: dt('cascadeselect.lg.font.size');\n        width: dt('cascadeselect.lg.font.size');\n        height: dt('cascadeselect.lg.font.size');\n    }\n\n    .p-cascadeselect-fluid {\n        display: flex;\n    }\n\n    .p-cascadeselect-fluid .p-cascadeselect-label {\n        width: 1%;\n    }\n\n    .p-cascadeselect-fluid .p-cascadeselect-overlay .p-cascadeselect-overlay {\n         min-width: 12.5rem;\n    }\n";export{style};//# sourceMappingURL=index.mjs.map