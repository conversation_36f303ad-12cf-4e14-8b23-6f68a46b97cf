var style="\n    .p-metergroup {\n        display: flex;\n        gap: dt('metergroup.gap');\n    }\n\n    .p-metergroup-meters {\n        display: flex;\n        background: dt('metergroup.meters.background');\n        border-radius: dt('metergroup.border.radius');\n    }\n\n    .p-metergroup-label-list {\n        display: flex;\n        flex-wrap: wrap;\n        margin: 0;\n        padding: 0;\n        list-style-type: none;\n    }\n\n    .p-metergroup-label {\n        display: inline-flex;\n        align-items: center;\n        gap: dt('metergroup.label.gap');\n    }\n\n    .p-metergroup-label-marker {\n        display: inline-flex;\n        width: dt('metergroup.label.marker.size');\n        height: dt('metergroup.label.marker.size');\n        border-radius: 100%;\n    }\n\n    .p-metergroup-label-icon {\n        font-size: dt('metergroup.label.icon.size');\n        width: dt('metergroup.label.icon.size');\n        height: dt('metergroup.label.icon.size');\n    }\n\n    .p-metergroup-horizontal {\n        flex-direction: column;\n    }\n\n    .p-metergroup-label-list-horizontal {\n        gap: dt('metergroup.label.list.horizontal.gap');\n    }\n\n    .p-metergroup-horizontal .p-metergroup-meters {\n        height: dt('metergroup.meters.size');\n    }\n\n    .p-metergroup-horizontal .p-metergroup-meter:first-of-type {\n        border-start-start-radius: dt('metergroup.border.radius');\n        border-end-start-radius: dt('metergroup.border.radius');\n    }\n\n    .p-metergroup-horizontal .p-metergroup-meter:last-of-type {\n        border-start-end-radius: dt('metergroup.border.radius');\n        border-end-end-radius: dt('metergroup.border.radius');\n    }\n\n    .p-metergroup-vertical {\n        flex-direction: row;\n    }\n\n    .p-metergroup-label-list-vertical {\n        flex-direction: column;\n        gap: dt('metergroup.label.list.vertical.gap');\n    }\n\n    .p-metergroup-vertical .p-metergroup-meters {\n        flex-direction: column;\n        width: dt('metergroup.meters.size');\n        height: 100%;\n    }\n\n    .p-metergroup-vertical .p-metergroup-label-list {\n        align-items: flex-start;\n    }\n\n    .p-metergroup-vertical .p-metergroup-meter:first-of-type {\n        border-start-start-radius: dt('metergroup.border.radius');\n        border-start-end-radius: dt('metergroup.border.radius');\n    }\n\n    .p-metergroup-vertical .p-metergroup-meter:last-of-type {\n        border-end-start-radius: dt('metergroup.border.radius');\n        border-end-end-radius: dt('metergroup.border.radius');\n    }\n";export{style};//# sourceMappingURL=index.mjs.map