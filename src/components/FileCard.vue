<template>
  <div
    class="file-card surface-0 border-round-lg overflow-hidden cursor-pointer transition-all transition-duration-200 hover:shadow-3 relative"
    :class="{ 'selected-card': selected }"
    @click="handleCardClick"
  >
    <!-- Menu <PERSON>ton -->
    <div class="absolute top-0 right-0 p-2 z-2">
      <Button 
        icon="pi pi-ellipsis-h" 
        severity="secondary" 
        text 
        rounded 
        size="small"
        class="menu-button bg-black-alpha-60 text-white hover:bg-black-alpha-80"
        @click.stop="toggleMenu"
        aria-haspopup="true"
        aria-controls="overlay_menu"
      />
      <Menu 
        ref="menu" 
        id="overlay_menu" 
        :model="menuItems" 
        :popup="true" 
        @hide="isMenuVisible = false"
      />
    </div>

    <!-- Thumbnail -->
    <div class="thumbnail-container relative">
      <img 
        :src="thumbnailSrc" 
        :alt="file.title"
        class="w-full h-12rem object-cover"
        @error="handleImageError"
      />
      <!-- Gradient overlay for menu button visibility -->
      <div class="absolute top-0 right-0 w-4rem h-4rem bg-gradient-to-bl from-black-alpha-60 to-transparent"></div>
    </div>

    <!-- Content -->
    <div class="p-3">
      <!-- File type icon and title -->
      <div class="flex align-items-center gap-2 mb-2">
        <i class="pi pi-file text-400 text-sm"></i>
        <h3 class="text-900 font-medium text-base m-0 flex-1 overflow-hidden text-overflow-ellipsis white-space-nowrap">
          {{ file.title }}
        </h3>
      </div>

      <!-- Metadata -->
      <div class="flex align-items-center justify-content-between text-sm text-600">
        <span>Edited by {{ file.editedBy }}</span>
        <span>{{ file.lastModified }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Button from 'primevue/button'
import Menu from 'primevue/menu'

// Props
const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['click', 'menu-action'])

// Refs
const menu = ref()
const isMenuVisible = ref(false)

// Computed
const thumbnailSrc = computed(() => {
  // You can replace this with actual image URLs or use a placeholder service
  return `https://picsum.photos/280/200?random=${props.file.id}`
})

const menuItems = ref([
  {
    label: 'Open',
    icon: 'pi pi-external-link',
    command: () => handleMenuAction('open')
  },
  {
    label: 'Rename',
    icon: 'pi pi-pencil',
    command: () => handleMenuAction('rename')
  },
  {
    label: 'Duplicate',
    icon: 'pi pi-copy',
    command: () => handleMenuAction('duplicate')
  },
  {
    label: 'Download',
    icon: 'pi pi-download',
    command: () => handleMenuAction('download')
  },
  {
    separator: true
  },
  {
    label: 'Delete',
    icon: 'pi pi-trash',
    class: 'text-red-500',
    command: () => handleMenuAction('delete')
  }
])

// Methods
const handleCardClick = () => {
  if (!isMenuVisible.value) {
    emit('click', props.file)
  }
}

const toggleMenu = (event) => {
  isMenuVisible.value = !isMenuVisible.value
  menu.value.toggle(event)
}

const handleMenuAction = (action) => {
  emit('menu-action', action, props.file)
  isMenuVisible.value = false
}

const handleImageError = (event) => {
  // Fallback to a placeholder if image fails to load
  event.target.src = `https://via.placeholder.com/280x200/e2e8f0/64748b?text=${encodeURIComponent(props.file.title)}`
}
</script>

<style scoped>
.file-card {
  min-height: 280px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.file-card:hover {
  transform: translateY(-2px);
}

.selected-card {
  border: 2px solid #8b5cf6 !important;
  box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.2);
}

.menu-button {
  backdrop-filter: blur(4px);
}

.thumbnail-container {
  overflow: hidden;
}

.thumbnail-container img {
  transition: transform 0.2s ease;
}

.file-card:hover .thumbnail-container img {
  transform: scale(1.05);
}

/* Custom gradient class */
.bg-gradient-to-bl {
  background: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.from-black-alpha-60 {
  --tw-gradient-from: rgba(0, 0, 0, 0.6);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
}

.to-transparent {
  --tw-gradient-to: transparent;
}
</style>