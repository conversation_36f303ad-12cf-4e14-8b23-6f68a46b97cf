<template>
  <div 
    class="file-details-drawer fixed top-0 right-0 h-screen bg-white shadow-4 z-5 transition-transform transition-duration-300"
    :class="{ 'translate-x-full': !visible, 'translate-x-0': visible }"
  >
    <!-- Header -->
    <div class="flex align-items-center justify-content-between px-4 py-3 border-bottom-1 border-200">
      <h3 class="text-lg font-semibold text-900 m-0">File Details</h3>
      <Button
        icon="pi pi-times"
        severity="secondary"
        text
        rounded
        size="small"
        @click="handleClose"
      />
    </div>

    <!-- Content -->
    <div v-if="file" class="p-4 h-full overflow-y-auto pb-8">
      <!-- Thumbnail -->
      <div class="mb-4">
        <img 
          :src="thumbnailSrc" 
          :alt="file.title"
          class="w-full border-round-lg object-cover"
          style="height: 200px;"
          @error="handleImageError"
        />
      </div>

      <!-- File Details -->
      <div class="mb-6">
        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Name</label>
          <p class="text-base text-900 m-0 font-medium">{{ file.title }}</p>
        </div>

        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Last modified</label>
          <p class="text-base text-900 m-0">{{ file.lastModified }}</p>
        </div>

        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Size</label>
          <p class="text-base text-900 m-0">{{ file.size }}</p>
        </div>

        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Dimensions</label>
          <p class="text-base text-900 m-0">{{ file.dimensions }}</p>
        </div>

        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Type</label>
          <p class="text-base text-900 m-0">{{ file.type }}</p>
        </div>

        <div class="detail-item mb-3">
          <label class="text-sm font-medium text-600 block mb-1">Location</label>
          <p class="text-base text-900 m-0">{{ file.location }}</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-column gap-3">
        <Button 
          label="Open" 
          icon="pi pi-external-link"
          class="bg-purple-600 hover:bg-purple-700 border-purple-600 w-full"
          @click="handleAction('open')"
        />
        
        <Button 
          label="Duplicate" 
          icon="pi pi-copy"
          severity="secondary"
          outlined
          class="w-full"
          @click="handleAction('duplicate')"
        />
        
        <Button 
          label="Rename" 
          icon="pi pi-pencil"
          severity="secondary"
          outlined
          class="w-full"
          @click="handleAction('rename')"
        />
        
        <Button 
          label="Delete" 
          icon="pi pi-trash"
          severity="danger"
          outlined
          class="w-full"
          @click="handleAction('delete')"
        />
      </div>
    </div>

    <!-- Empty state -->
    <div v-else class="flex flex-column align-items-center justify-content-center h-full text-center p-4">
      <i class="pi pi-file text-6xl text-300 mb-3"></i>
      <p class="text-500 m-0">Select a file to view details</p>
    </div>
  </div>

  <!-- Backdrop -->
  <div 
    v-if="visible" 
    class="fixed top-0 left-0 w-screen h-screen bg-black-alpha-30 z-4"
    @click="handleClose"
  ></div>
</template>

<script setup>
import { computed } from 'vue'
import Button from 'primevue/button'

// Props
const props = defineProps({
  file: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'action'])

// Computed
const thumbnailSrc = computed(() => {
  if (!props.file) return ''
  return `https://picsum.photos/400/200?random=${props.file.id}`
})

// Methods
const handleClose = () => {
  emit('close')
}

const handleAction = (action) => {
  emit('action', action, props.file)
  
  // Close drawer for certain actions
  if (action === 'open') {
    handleClose()
  }
}

const handleImageError = (event) => {
  if (props.file) {
    event.target.src = `https://via.placeholder.com/400x200/e2e8f0/64748b?text=${encodeURIComponent(props.file.title)}`
  }
}
</script>

<style scoped>
.file-details-drawer {
  width: 480px;
  border-left: 1px solid #e5e7eb;
}

.detail-item {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 0.75rem;
}

.detail-item:last-child {
  border-bottom: none;
}

/* Smooth transitions */
.translate-x-full {
  transform: translateX(100%);
}

.translate-x-0 {
  transform: translateX(0);
}

/* Responsive */
@media (max-width: 768px) {
  .file-details-drawer {
    width: 100vw;
  }
}

/* Backdrop animation */
.bg-black-alpha-30 {
  background-color: rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>